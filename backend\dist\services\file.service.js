"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileService = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const env_1 = require("../config/env");
const crypto_1 = __importDefault(require("crypto"));
class FileService {
    static async saveFile(buffer, fileName) {
        try {
            await FileService.ensureUploadDir();
            const filePath = path_1.default.join(env_1.config.upload.dir, fileName);
            await promises_1.default.writeFile(filePath, buffer);
            return filePath;
        }
        catch (error) {
            console.error('保存文件失败:', error);
            throw new Error('文件保存失败');
        }
    }
    static async readFile(filePath) {
        try {
            return await promises_1.default.readFile(filePath);
        }
        catch (error) {
            console.error('读取文件失败:', error);
            throw new Error('文件读取失败');
        }
    }
    static async deleteFile(filePath) {
        try {
            await promises_1.default.unlink(filePath);
        }
        catch (error) {
            console.error('删除文件失败:', error);
        }
    }
    static async fileExists(filePath) {
        try {
            await promises_1.default.access(filePath);
            return true;
        }
        catch {
            return false;
        }
    }
    static async getFileInfo(filePath) {
        try {
            const stats = await promises_1.default.stat(filePath);
            return {
                size: stats.size,
                mtime: stats.mtime,
                isFile: stats.isFile(),
            };
        }
        catch (error) {
            console.error('获取文件信息失败:', error);
            throw new Error('获取文件信息失败');
        }
    }
    static async ensureUploadDir() {
        try {
            await promises_1.default.access(env_1.config.upload.dir);
        }
        catch {
            await promises_1.default.mkdir(env_1.config.upload.dir, { recursive: true });
        }
    }
    static generateSafeFileName(originalName, hash) {
        const ext = path_1.default.extname(originalName);
        const baseName = hash || crypto_1.default.randomUUID();
        return `${baseName}${ext}`;
    }
    static isValidImageType(mimeType) {
        const allowedTypes = env_1.config.upload.allowedMimeTypes;
        return allowedTypes.includes(mimeType);
    }
    static isValidFileSize(size, maxSize) {
        const limit = maxSize || env_1.config.upload.maxFileSize;
        return size <= limit;
    }
    static async calculateFileHash(filePath, algorithm = 'sha256') {
        try {
            const buffer = await promises_1.default.readFile(filePath);
            return crypto_1.default.createHash(algorithm).update(buffer).digest('hex');
        }
        catch (error) {
            console.error('计算文件哈希失败:', error);
            throw new Error('计算文件哈希失败');
        }
    }
    static calculateBufferHash(buffer, algorithm = 'sha256') {
        return crypto_1.default.createHash(algorithm).update(buffer).digest('hex');
    }
    static async createBackup(filePath) {
        try {
            const backupPath = `${filePath}.backup.${Date.now()}`;
            const buffer = await promises_1.default.readFile(filePath);
            await promises_1.default.writeFile(backupPath, buffer);
            return backupPath;
        }
        catch (error) {
            console.error('创建文件备份失败:', error);
            throw new Error('创建文件备份失败');
        }
    }
    static async cleanupTempFiles(olderThanHours = 24) {
        try {
            const tempDir = path_1.default.join(env_1.config.upload.dir, 'temp');
            const tempExists = await FileService.fileExists(tempDir);
            if (!tempExists) {
                return;
            }
            const files = await promises_1.default.readdir(tempDir);
            const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
            for (const file of files) {
                const filePath = path_1.default.join(tempDir, file);
                const stats = await promises_1.default.stat(filePath);
                if (stats.mtime < cutoffTime) {
                    await promises_1.default.unlink(filePath);
                    console.log(`清理临时文件: ${filePath}`);
                }
            }
        }
        catch (error) {
            console.error('清理临时文件失败:', error);
        }
    }
    static async getDirectorySize(dirPath) {
        try {
            let totalSize = 0;
            const files = await promises_1.default.readdir(dirPath, { withFileTypes: true });
            for (const file of files) {
                const filePath = path_1.default.join(dirPath, file.name);
                if (file.isDirectory()) {
                    totalSize += await FileService.getDirectorySize(filePath);
                }
                else {
                    const stats = await promises_1.default.stat(filePath);
                    totalSize += stats.size;
                }
            }
            return totalSize;
        }
        catch (error) {
            console.error('获取目录大小失败:', error);
            return 0;
        }
    }
    static formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        return `${size.toFixed(2)} ${units[unitIndex]}`;
    }
    static async verifyFileIntegrity(filePath, expectedHash) {
        try {
            const actualHash = await FileService.calculateFileHash(filePath);
            return actualHash === expectedHash;
        }
        catch (error) {
            console.error('验证文件完整性失败:', error);
            return false;
        }
    }
    static async createFileCopies(sourcePath, destinationPaths) {
        const success = [];
        const failed = [];
        try {
            const buffer = await promises_1.default.readFile(sourcePath);
            for (const destPath of destinationPaths) {
                try {
                    const destDir = path_1.default.dirname(destPath);
                    await promises_1.default.mkdir(destDir, { recursive: true });
                    await promises_1.default.writeFile(destPath, buffer);
                    success.push(destPath);
                }
                catch (error) {
                    console.error(`复制文件到 ${destPath} 失败:`, error);
                    failed.push(destPath);
                }
            }
        }
        catch (error) {
            console.error('读取源文件失败:', error);
            failed.push(...destinationPaths);
        }
        return { success, failed };
    }
    static getMimeTypeFromExtension(fileName) {
        const ext = path_1.default.extname(fileName).toLowerCase();
        const mimeTypes = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp',
            '.svg': 'image/svg+xml',
            '.ico': 'image/x-icon',
        };
        return mimeTypes[ext] || 'application/octet-stream';
    }
    static generateThumbnailName(originalName, size) {
        const ext = path_1.default.extname(originalName);
        const baseName = path_1.default.basename(originalName, ext);
        return `${baseName}_thumb_${size}${ext}`;
    }
}
exports.FileService = FileService;
//# sourceMappingURL=file.service.js.map