"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogService = void 0;
const database_1 = require("../config/database");
class LogService {
    static initialize(socketService) {
        LogService.socketService = socketService;
    }
    static async logSystemEvent(eventType, eventLevel, eventMessage, eventData, serverInstance) {
        try {
            const log = await database_1.prisma.systemEventLog.create({
                data: {
                    eventType,
                    eventLevel,
                    eventMessage,
                    eventData: eventData ? JSON.stringify(eventData) : null,
                    serverInstance: serverInstance || require('os').hostname(),
                }
            });
            const logData = {
                id: log.id,
                eventType: log.eventType,
                eventLevel: log.eventLevel,
                eventMessage: log.eventMessage,
                eventData: log.eventData ? JSON.parse(log.eventData) : null,
                serverInstance: log.serverInstance || undefined,
                createdAt: log.createdAt.toISOString(),
            };
            if (LogService.socketService) {
                LogService.socketService.broadcastSystemLog(logData);
            }
            console.log(`[${eventLevel.toUpperCase()}] ${eventType}: ${eventMessage}`);
        }
        catch (error) {
            console.error('记录系统日志失败:', error);
        }
    }
    static async logUploadAction(userId, imageId, actionType, fileName, fileSize, ipAddress, userAgent, isSuccess = true, errorMessage) {
        try {
            const log = await database_1.prisma.uploadLog.create({
                data: {
                    userId,
                    imageId,
                    actionType,
                    fileName,
                    fileSize,
                    ipAddress,
                    userAgent,
                    isSuccess,
                    errorMessage,
                },
                include: {
                    user: {
                        select: {
                            username: true,
                        }
                    }
                }
            });
            const logData = {
                id: log.id,
                userId: log.userId || 0,
                username: log.user?.username,
                imageId: log.imageId || undefined,
                actionType: log.actionType,
                fileName: log.fileName || undefined,
                fileSize: log.fileSize ? Number(log.fileSize) : undefined,
                ipAddress: log.ipAddress || '',
                userAgent: log.userAgent || undefined,
                isSuccess: log.isSuccess,
                errorMessage: log.errorMessage || undefined,
                createdAt: log.createdAt.toISOString(),
            };
            if (LogService.socketService) {
                LogService.socketService.broadcastUploadLog(logData);
            }
        }
        catch (error) {
            console.error('记录上传日志失败:', error);
        }
    }
    static async logImageAccess(imageId, ipAddress, userAgent, referer, responseTime) {
        try {
            const log = await database_1.prisma.accessLog.create({
                data: {
                    imageId,
                    ipAddress,
                    userAgent,
                    referer,
                    responseTime,
                }
            });
            const logData = {
                id: log.id,
                imageId: log.imageId,
                ipAddress: log.ipAddress,
                userAgent: log.userAgent || undefined,
                referer: log.referer || undefined,
                country: log.country || undefined,
                city: log.city || undefined,
                responseTime: log.responseTime || undefined,
                createdAt: log.createdAt.toISOString(),
            };
            if (LogService.socketService) {
                LogService.socketService.broadcastAccessLog(logData);
            }
        }
        catch (error) {
            console.error('记录访问日志失败:', error);
        }
    }
    static async logAdminOperation(adminId, operationType, targetType, targetId, operationDetails, ipAddress, userAgent) {
        try {
            const log = await database_1.prisma.adminOperationLog.create({
                data: {
                    adminId,
                    operationType,
                    targetType,
                    targetId,
                    operationDetails: operationDetails ? JSON.stringify(operationDetails) : null,
                    ipAddress,
                    userAgent,
                },
                include: {
                    admin: {
                        select: {
                            username: true,
                        }
                    }
                }
            });
            const logData = {
                id: log.id,
                adminId: log.adminId,
                adminUsername: log.admin.username,
                operationType: log.operationType,
                targetType: log.targetType || undefined,
                targetId: log.targetId || undefined,
                operationDetails: log.operationDetails ? JSON.parse(log.operationDetails) : undefined,
                ipAddress: log.ipAddress || undefined,
                userAgent: log.userAgent || undefined,
                createdAt: log.createdAt.toISOString(),
            };
            if (LogService.socketService) {
                LogService.socketService.broadcastAdminLog(logData);
            }
        }
        catch (error) {
            console.error('记录管理员操作日志失败:', error);
        }
    }
    static async getSystemLogs(params) {
        try {
            const { page = 1, limit = 50, filters } = params;
            const skip = (page - 1) * limit;
            const where = {};
            if (filters?.levels?.length) {
                where.eventLevel = { in: filters.levels };
            }
            if (filters?.types?.length) {
                where.eventType = { in: filters.types };
            }
            if (filters?.startDate || filters?.endDate) {
                where.createdAt = {};
                if (filters.startDate) {
                    where.createdAt.gte = new Date(filters.startDate);
                }
                if (filters.endDate) {
                    where.createdAt.lte = new Date(filters.endDate);
                }
            }
            const [logs, total] = await Promise.all([
                database_1.prisma.systemEventLog.findMany({
                    where,
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take: limit,
                }),
                database_1.prisma.systemEventLog.count({ where })
            ]);
            const logData = logs.map(log => ({
                id: log.id,
                eventType: log.eventType,
                eventLevel: log.eventLevel,
                eventMessage: log.eventMessage,
                eventData: log.eventData ? JSON.parse(log.eventData) : null,
                serverInstance: log.serverInstance || undefined,
                createdAt: log.createdAt.toISOString(),
            }));
            return {
                logs: logData,
                total,
                page,
                limit,
            };
        }
        catch (error) {
            console.error('获取系统日志失败:', error);
            throw error;
        }
    }
    static async getUploadLogs(params) {
        try {
            const { page = 1, limit = 50, filters } = params;
            const skip = (page - 1) * limit;
            const where = {};
            if (filters?.userId) {
                where.userId = filters.userId;
            }
            if (filters?.ipAddress) {
                where.ipAddress = { contains: filters.ipAddress };
            }
            if (filters?.startDate || filters?.endDate) {
                where.createdAt = {};
                if (filters.startDate) {
                    where.createdAt.gte = new Date(filters.startDate);
                }
                if (filters.endDate) {
                    where.createdAt.lte = new Date(filters.endDate);
                }
            }
            const [logs, total] = await Promise.all([
                database_1.prisma.uploadLog.findMany({
                    where,
                    include: {
                        user: {
                            select: {
                                username: true,
                            }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take: limit,
                }),
                database_1.prisma.uploadLog.count({ where })
            ]);
            const logData = logs.map(log => ({
                id: log.id,
                userId: log.userId || 0,
                username: log.user?.username,
                imageId: log.imageId || undefined,
                actionType: log.actionType,
                fileName: log.fileName || undefined,
                fileSize: log.fileSize ? Number(log.fileSize) : undefined,
                ipAddress: log.ipAddress || '',
                userAgent: log.userAgent || undefined,
                isSuccess: log.isSuccess,
                errorMessage: log.errorMessage || undefined,
                createdAt: log.createdAt.toISOString(),
            }));
            return {
                logs: logData,
                total,
                page,
                limit,
            };
        }
        catch (error) {
            console.error('获取上传日志失败:', error);
            throw error;
        }
    }
}
exports.LogService = LogService;
//# sourceMappingURL=log.service.js.map