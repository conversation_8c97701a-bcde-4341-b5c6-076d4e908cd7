"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketService = void 0;
const socket_io_1 = require("socket.io");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("./config");
const database_1 = require("./database");
class SocketService {
    constructor(httpServer) {
        this.connectedAdmins = new Set();
        this.io = new socket_io_1.Server(httpServer, {
            cors: {
                origin: process.env.FRONTEND_URL || "http://localhost:5173",
                methods: ["GET", "POST"],
                credentials: true
            },
            path: '/socket.io',
            transports: ['websocket', 'polling'],
            pingTimeout: 60000,
            pingInterval: 25000,
        });
        this.setupMiddleware();
        this.setupEventHandlers();
    }
    static getInstance(httpServer) {
        if (!SocketService.instance && httpServer) {
            SocketService.instance = new SocketService(httpServer);
        }
        return SocketService.instance;
    }
    setupMiddleware() {
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
                if (!token) {
                    return next(new Error('认证令牌缺失'));
                }
                const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwtSecret);
                const user = await database_1.prisma.user.findUnique({
                    where: { id: decoded.userId },
                    select: {
                        id: true,
                        username: true,
                        role: true,
                        status: true,
                    }
                });
                if (!user) {
                    return next(new Error('用户不存在'));
                }
                if (user.status !== 'active') {
                    return next(new Error('用户账户已被禁用'));
                }
                if (user.role !== 'admin') {
                    return next(new Error('权限不足，仅管理员可访问'));
                }
                socket.data = {
                    userId: user.id,
                    username: user.username,
                    role: user.role,
                    isAdmin: true,
                };
                next();
            }
            catch (error) {
                console.error('Socket 认证失败:', error);
                next(new Error('认证失败'));
            }
        });
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`管理员 ${socket.data.username} 连接到实时日志系统`);
            socket.join('admin-room');
            this.connectedAdmins.add(socket.id);
            socket.emit('connection:status', {
                connected: true,
                timestamp: new Date().toISOString(),
                clientsCount: this.connectedAdmins.size,
            });
            socket.on('logs:subscribe', (filters) => {
                console.log(`管理员 ${socket.data.username} 订阅日志，过滤条件:`, filters);
                socket.join('logs-subscribers');
            });
            socket.on('logs:unsubscribe', () => {
                console.log(`管理员 ${socket.data.username} 取消订阅日志`);
                socket.leave('logs-subscribers');
            });
            socket.on('logs:history', async (params, callback) => {
                try {
                    callback({ success: true, data: [], total: 0 });
                }
                catch (error) {
                    callback({ success: false, error: '获取历史日志失败' });
                }
            });
            socket.on('monitoring:subscribe', () => {
                console.log(`管理员 ${socket.data.username} 订阅系统监控`);
                socket.join('monitoring-subscribers');
            });
            socket.on('monitoring:unsubscribe', () => {
                console.log(`管理员 ${socket.data.username} 取消订阅系统监控`);
                socket.leave('monitoring-subscribers');
            });
            socket.on('disconnect', (reason) => {
                console.log(`管理员 ${socket.data.username} 断开连接，原因: ${reason}`);
                this.connectedAdmins.delete(socket.id);
                this.io.to('admin-room').emit('connection:status', {
                    connected: false,
                    timestamp: new Date().toISOString(),
                    clientsCount: this.connectedAdmins.size,
                });
            });
        });
    }
    broadcastSystemLog(log) {
        this.io.to('logs-subscribers').emit('log:system', log);
    }
    broadcastUploadLog(log) {
        this.io.to('logs-subscribers').emit('log:upload', log);
    }
    broadcastAccessLog(log) {
        this.io.to('logs-subscribers').emit('log:access', log);
    }
    broadcastAdminLog(log) {
        this.io.to('logs-subscribers').emit('log:admin', log);
    }
    broadcastSystemMonitoring(data) {
        this.io.to('monitoring-subscribers').emit('monitoring:system', data);
    }
    broadcastSystemAlert(alert) {
        this.io.to('admin-room').emit('monitoring:alert', alert);
    }
    getConnectedAdminsCount() {
        return this.connectedAdmins.size;
    }
    close() {
        this.io.close();
    }
}
exports.SocketService = SocketService;
//# sourceMappingURL=socket.js.map