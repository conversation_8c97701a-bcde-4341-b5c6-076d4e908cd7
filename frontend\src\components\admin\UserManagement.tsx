import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Input } from '../ui/Input';
import { UserDetailModal } from './UserDetailModal';
import { adminService } from '../../services/admin.service';
import type { User, UsersResponse } from '../../services/admin.service';



export function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLevel, setFilterLevel] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [batchAction, setBatchAction] = useState('');

  useEffect(() => {
    loadUsers();
  }, [currentPage, searchTerm, filterLevel, filterStatus]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await adminService.getUsers({
        page: currentPage,
        limit: 20,
        search: searchTerm,
        level: filterLevel,
        status: filterStatus
      });

      setUsers(response.users);
      setTotalPages(response.pagination.totalPages);
    } catch (error) {
      console.error('加载用户列表失败:', error);
      setUsers([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleUserUpdated = (updatedUser: User) => {
    setUsers(users.map(user =>
      user.id === updatedUser.id ? updatedUser : user
    ));
  };

  const handleViewDetails = (userId: number) => {
    setSelectedUserId(userId);
    setIsDetailModalOpen(true);
  };

  const handleSelectUser = (userId: number) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map(user => user.id));
    }
  };

  const handleBatchAction = async () => {
    if (!batchAction || selectedUsers.length === 0) return;

    try {
      setLoading(true);

      for (const userId of selectedUsers) {
        if (batchAction.startsWith('status-')) {
          const status = batchAction.replace('status-', '');
          await adminService.updateUserStatus(userId, status);
        } else if (batchAction.startsWith('level-')) {
          const level = batchAction.replace('level-', '');
          await adminService.updateUserLevel(userId, level);
        }
      }

      // 重新加载用户列表
      await loadUsers();
      setSelectedUsers([]);
      setBatchAction('');

      alert(`批量操作完成，已处理 ${selectedUsers.length} 个用户`);
    } catch (error) {
      console.error('批量操作失败:', error);
      alert('批量操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleLevelChange = async (userId: number, newLevel: string) => {
    try {
      await adminService.updateUserLevel(userId, newLevel);

      // 更新本地状态
      setUsers(users.map(user =>
        user.id === userId
          ? { ...user, userLevel: newLevel }
          : user
      ));

      console.log(`用户 ${userId} 等级已更新为 ${newLevel}`);
    } catch (error) {
      console.error('更新用户等级失败:', error);
      alert('更新用户等级失败，请重试');
    }
  };

  const handleStatusChange = async (userId: number, newStatus: 'active' | 'suspended' | 'banned') => {
    try {
      await adminService.updateUserStatus(userId, newStatus);

      // 更新本地状态
      setUsers(users.map(user =>
        user.id === userId
          ? { ...user, status: newStatus }
          : user
      ));

      console.log(`用户 ${userId} 状态已更新为 ${newStatus}`);
    } catch (error) {
      console.error('更新用户状态失败:', error);
      alert('更新用户状态失败，请重试');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getLevelBadgeColor = (level: string) => {
    switch (level) {
      case 'free': return 'bg-gray-100 text-gray-800';
      case 'vip1': return 'bg-blue-100 text-blue-800';
      case 'vip2': return 'bg-purple-100 text-purple-800';
      case 'vip3': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'suspended': return 'bg-yellow-100 text-yellow-800';
      case 'banned': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };



  return (
    <div className="space-y-6">
      {/* 页面标题和统计 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">用户管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的所有用户账号</p>
        </div>
        <div className="flex space-x-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{users.length}</div>
            <div className="text-gray-500">当前页用户</div>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <Input
                placeholder="搜索用户名或邮箱..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <select
                value={filterLevel}
                onChange={(e) => setFilterLevel(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">所有等级</option>
                <option value="free">免费用户</option>
                <option value="vip1">VIP1</option>
                <option value="vip2">VIP2</option>
                <option value="vip3">VIP3</option>
              </select>
            </div>
            <div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">所有状态</option>
                <option value="active">正常</option>
                <option value="suspended">暂停</option>
                <option value="banned">封禁</option>
              </select>
            </div>
            <div>
              <Button onClick={() => loadUsers()} disabled={loading}>
                {loading ? '刷新中...' : '刷新'}
              </Button>
            </div>
          </div>

          {/* 批量操作 */}
          {selectedUsers.length > 0 && (
            <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
              <span className="text-sm font-medium text-blue-900">
                已选择 {selectedUsers.length} 个用户
              </span>
              <select
                value={batchAction}
                onChange={(e) => setBatchAction(e.target.value)}
                className="px-3 py-1 border border-blue-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">选择批量操作</option>
                <optgroup label="修改状态">
                  <option value="status-active">设为正常</option>
                  <option value="status-suspended">设为暂停</option>
                  <option value="status-banned">设为封禁</option>
                </optgroup>
                <optgroup label="修改等级">
                  <option value="level-free">设为免费用户</option>
                  <option value="level-vip1">设为VIP1</option>
                  <option value="level-vip2">设为VIP2</option>
                  <option value="level-vip3">设为VIP3</option>
                </optgroup>
              </select>
              <Button
                size="sm"
                onClick={handleBatchAction}
                disabled={!batchAction || loading}
              >
                执行操作
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setSelectedUsers([])}
              >
                取消选择
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 用户列表 */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>用户列表 ({users.length} 个用户)</CardTitle>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedUsers.length === users.length && users.length > 0}
                onChange={handleSelectAll}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600">全选</span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-2">📝</div>
              <p className="text-gray-500">暂无用户数据</p>
            </div>
          ) : (
            <div className="space-y-3">
              {users.map((user) => (
                <div key={user.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    {/* 复选框 */}
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleSelectUser(user.id)}
                      disabled={user.role === 'admin'}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                    />

                    {/* 用户头像 */}
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold text-lg">
                        {user.username.charAt(0).toUpperCase()}
                      </span>
                    </div>

                    {/* 用户信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {user.username}
                        </h3>
                        {user.role === 'admin' && (
                          <Badge className="bg-red-100 text-red-800 text-xs">
                            管理员
                          </Badge>
                        )}
                      </div>
                      <p className="text-gray-600 text-sm truncate">{user.email}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        <span>上传: {user.totalUploads}次</span>
                        <span>存储: {formatFileSize(user.storageUsed)}</span>
                        <span>注册: {formatDate(user.createdAt)}</span>
                      </div>
                    </div>

                    {/* 等级和状态 */}
                    <div className="flex flex-col space-y-2">
                      <Badge className={getLevelBadgeColor(user.userLevel)}>
                        {user.userLevel === 'free' ? '免费' : user.userLevel.toUpperCase()}
                      </Badge>
                      <Badge className={getStatusBadgeColor(user.status)}>
                        {user.status === 'active' ? '正常' :
                         user.status === 'suspended' ? '暂停' : '封禁'}
                      </Badge>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex flex-col space-y-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewDetails(user.id)}
                      >
                        查看详情
                      </Button>
                      {user.role !== 'admin' && (
                        <select
                          value={user.status}
                          onChange={(e) => handleStatusChange(user.id, e.target.value)}
                          className="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="active">正常</option>
                          <option value="suspended">暂停</option>
                          <option value="banned">封禁</option>
                        </select>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 分页 */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                第 {currentPage} 页，共 {totalPages} 页
              </div>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 用户详情模态框 */}
      <UserDetailModal
        userId={selectedUserId}
        isOpen={isDetailModalOpen}
        onClose={() => {
          setIsDetailModalOpen(false);
          setSelectedUserId(null);
        }}
        onUserUpdated={handleUserUpdated}
      />
    </div>
  );
}
