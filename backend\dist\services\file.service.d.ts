export declare class FileService {
    static saveFile(buffer: Buffer, fileName: string): Promise<string>;
    static readFile(filePath: string): Promise<Buffer>;
    static deleteFile(filePath: string): Promise<void>;
    static fileExists(filePath: string): Promise<boolean>;
    static getFileInfo(filePath: string): Promise<{
        size: number;
        mtime: Date;
        isFile: boolean;
    }>;
    private static ensureUploadDir;
    static generateSafeFileName(originalName: string, hash?: string): string;
    static isValidImageType(mimeType: string): boolean;
    static isValidFileSize(size: number, maxSize?: number): boolean;
    static calculateFileHash(filePath: string, algorithm?: string): Promise<string>;
    static calculateBufferHash(buffer: Buffer, algorithm?: string): string;
    static createBackup(filePath: string): Promise<string>;
    static cleanupTempFiles(olderThanHours?: number): Promise<void>;
    static getDirectorySize(dirPath: string): Promise<number>;
    static formatFileSize(bytes: number): string;
    static verifyFileIntegrity(filePath: string, expectedHash: string): Promise<boolean>;
    static createFileCopies(sourcePath: string, destinationPaths: string[]): Promise<{
        success: string[];
        failed: string[];
    }>;
    static getMimeTypeFromExtension(fileName: string): string;
    static generateThumbnailName(originalName: string, size: string): string;
}
//# sourceMappingURL=file.service.d.ts.map