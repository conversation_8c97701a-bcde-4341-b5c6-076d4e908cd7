import { Request, Response, NextFunction } from 'express';
export declare const uploadSingle: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const uploadMultiple: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const handleUploadError: (error: any, req: Request, res: Response, next: NextFunction) => void;
export declare const validateUploadedFile: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateUploadedFiles: (req: Request, res: Response, next: NextFunction) => void;
export declare const checkUploadLimits: (req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=upload.middleware.d.ts.map