import { prisma } from '../config/database';
import { SocketService } from '../config/socket';
import { 
  SystemLogData, 
  UploadLogData, 
  AccessLogData, 
  AdminLogData,
  LogFilters,
  LogHistoryParams 
} from '../config/socket';

export class LogService {
  private static socketService: SocketService;

  // 初始化日志服务
  public static initialize(socketService: SocketService): void {
    LogService.socketService = socketService;
  }

  // 记录并广播系统日志
  public static async logSystemEvent(
    eventType: string,
    eventLevel: 'debug' | 'info' | 'warning' | 'error' | 'critical',
    eventMessage: string,
    eventData?: any,
    serverInstance?: string
  ): Promise<void> {
    try {
      // 保存到数据库
      const log = await prisma.systemEventLog.create({
        data: {
          eventType,
          eventLevel,
          eventMessage,
          eventData: eventData ? JSON.stringify(eventData) : undefined,
          serverInstance: serverInstance || require('os').hostname(),
        }
      });

      // 构造广播数据
      const logData: SystemLogData = {
        id: log.id,
        eventType: log.eventType,
        eventLevel: log.eventLevel,
        eventMessage: log.eventMessage,
        eventData: log.eventData ? JSON.parse(log.eventData as string) : null,
        serverInstance: log.serverInstance || undefined,
        createdAt: log.createdAt.toISOString(),
      };

      // 实时广播
      if (LogService.socketService) {
        LogService.socketService.broadcastSystemLog(logData);
      }

      console.log(`[${eventLevel.toUpperCase()}] ${eventType}: ${eventMessage}`);
    } catch (error) {
      console.error('记录系统日志失败:', error);
    }
  }

  // 记录并广播上传日志
  public static async logUploadAction(
    userId: number,
    imageId: number | null,
    actionType: 'upload' | 'reuse' | 'failed',
    fileName?: string,
    fileSize?: number,
    ipAddress?: string,
    userAgent?: string,
    isSuccess: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    try {
      // 保存到数据库
      const log = await prisma.uploadLog.create({
        data: {
          userId,
          imageId,
          actionType,
          fileName: fileName || undefined,
          fileSize: fileSize ? BigInt(fileSize) : undefined,
          ipAddress: ipAddress || undefined,
          userAgent: userAgent || undefined,
          isSuccess,
          errorMessage: errorMessage || undefined,
        },
        include: {
          user: {
            select: {
              username: true,
            }
          }
        }
      });

      // 构造广播数据
      const logData: UploadLogData = {
        id: log.id,
        userId: log.userId || 0,
        username: log.user?.username || undefined,
        imageId: log.imageId || undefined,
        actionType: log.actionType,
        fileName: log.fileName || undefined,
        fileSize: log.fileSize ? Number(log.fileSize) : undefined,
        ipAddress: log.ipAddress || '',
        userAgent: log.userAgent || undefined,
        isSuccess: log.isSuccess,
        errorMessage: log.errorMessage || undefined,
        createdAt: log.createdAt.toISOString(),
      };

      // 实时广播
      if (LogService.socketService) {
        LogService.socketService.broadcastUploadLog(logData);
      }
    } catch (error) {
      console.error('记录上传日志失败:', error);
    }
  }

  // 记录并广播访问日志
  public static async logImageAccess(
    imageId: number,
    ipAddress: string,
    userAgent?: string,
    referer?: string,
    responseTime?: number
  ): Promise<void> {
    try {
      // 保存到数据库
      const log = await prisma.accessLog.create({
        data: {
          imageId,
          ipAddress,
          userAgent,
          referer,
          responseTime,
        }
      });

      // 构造广播数据
      const logData: AccessLogData = {
        id: log.id,
        imageId: log.imageId,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent || undefined,
        referer: log.referer || undefined,
        country: log.country || undefined,
        city: log.city || undefined,
        responseTime: log.responseTime || undefined,
        createdAt: log.createdAt.toISOString(),
      };

      // 实时广播
      if (LogService.socketService) {
        LogService.socketService.broadcastAccessLog(logData);
      }
    } catch (error) {
      console.error('记录访问日志失败:', error);
    }
  }

  // 记录并广播管理员操作日志
  public static async logAdminOperation(
    adminId: number,
    operationType: string,
    targetType?: string,
    targetId?: number,
    operationDetails?: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      // 保存到数据库
      const log = await prisma.adminOperationLog.create({
        data: {
          adminId,
          operationType,
          targetType,
          targetId,
          operationDetails: operationDetails ? JSON.stringify(operationDetails) : null,
          ipAddress,
          userAgent,
        },
        include: {
          admin: {
            select: {
              username: true,
            }
          }
        }
      });

      // 构造广播数据
      const logData: AdminLogData = {
        id: log.id,
        adminId: log.adminId,
        adminUsername: log.admin.username,
        operationType: log.operationType,
        targetType: log.targetType || undefined,
        targetId: log.targetId || undefined,
        operationDetails: log.operationDetails ? JSON.parse(log.operationDetails) : undefined,
        ipAddress: log.ipAddress || undefined,
        userAgent: log.userAgent || undefined,
        createdAt: log.createdAt.toISOString(),
      };

      // 实时广播
      if (LogService.socketService) {
        LogService.socketService.broadcastAdminLog(logData);
      }
    } catch (error) {
      console.error('记录管理员操作日志失败:', error);
    }
  }

  // 获取系统日志历史
  public static async getSystemLogs(params: LogHistoryParams): Promise<{
    logs: SystemLogData[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const { page = 1, limit = 50, filters } = params;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = {};
      
      if (filters?.levels?.length) {
        where.eventLevel = { in: filters.levels };
      }
      
      if (filters?.types?.length) {
        where.eventType = { in: filters.types };
      }
      
      if (filters?.startDate || filters?.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
          where.createdAt.gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          where.createdAt.lte = new Date(filters.endDate);
        }
      }

      // 查询数据
      const [logs, total] = await Promise.all([
        prisma.systemEventLog.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.systemEventLog.count({ where })
      ]);

      // 转换数据格式
      const logData: SystemLogData[] = logs.map(log => ({
        id: log.id,
        eventType: log.eventType,
        eventLevel: log.eventLevel,
        eventMessage: log.eventMessage,
        eventData: log.eventData ? JSON.parse(log.eventData) : null,
        serverInstance: log.serverInstance || undefined,
        createdAt: log.createdAt.toISOString(),
      }));

      return {
        logs: logData,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('获取系统日志失败:', error);
      throw error;
    }
  }

  // 获取上传日志历史
  public static async getUploadLogs(params: LogHistoryParams): Promise<{
    logs: UploadLogData[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const { page = 1, limit = 50, filters } = params;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = {};
      
      if (filters?.userId) {
        where.userId = filters.userId;
      }
      
      if (filters?.ipAddress) {
        where.ipAddress = { contains: filters.ipAddress };
      }
      
      if (filters?.startDate || filters?.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
          where.createdAt.gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          where.createdAt.lte = new Date(filters.endDate);
        }
      }

      // 查询数据
      const [logs, total] = await Promise.all([
        prisma.uploadLog.findMany({
          where,
          include: {
            user: {
              select: {
                username: true,
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.uploadLog.count({ where })
      ]);

      // 转换数据格式
      const logData: UploadLogData[] = logs.map(log => ({
        id: log.id,
        userId: log.userId || 0,
        username: log.user?.username,
        imageId: log.imageId || undefined,
        actionType: log.actionType,
        fileName: log.fileName || undefined,
        fileSize: log.fileSize ? Number(log.fileSize) : undefined,
        ipAddress: log.ipAddress || '',
        userAgent: log.userAgent || undefined,
        isSuccess: log.isSuccess,
        errorMessage: log.errorMessage || undefined,
        createdAt: log.createdAt.toISOString(),
      }));

      return {
        logs: logData,
        total,
        page,
        limit,
      };
    } catch (error) {
      console.error('获取上传日志失败:', error);
      throw error;
    }
  }
}
