import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>T<PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface LevelConfig {
  level: string;
  displayName: string;
  maxDailyUploads: number;
  maxFileSize: number;
  maxStorageSpace: number;
  canChooseProvider: boolean;
  prioritySupport: boolean;
  customDomain: boolean;
  visibleProviderCount: number;
}

interface Application {
  id: number;
  currentLevel: string;
  requestedLevel: string;
  reason: string;
  status: string;
  adminComment?: string;
  processedAt?: string;
  createdAt: string;
  admin?: {
    username: string;
    displayName: string;
  };
}

interface UserProfile {
  userLevel: string;
  levelExpiresAt?: string;
}

interface LevelUpgradeProps {
  userProfile: UserProfile;
  onUpdate: (updatedProfile: UserProfile) => void;
}

export function LevelUpgrade({ userProfile, onUpdate }: LevelUpgradeProps) {
  const [levelConfigs, setLevelConfigs] = useState<LevelConfig[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    requestedLevel: '',
    reason: '',
    contactInfo: '',
    businessInfo: '',
    expectedUsage: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      // 并行加载等级配置和申请历史
      const [configsResponse, applicationsResponse] = await Promise.all([
        fetch('/api/level-application/configs', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/level-application/my-applications', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ]);

      if (!configsResponse.ok || !applicationsResponse.ok) {
        throw new Error('加载数据失败');
      }

      const [configsData, applicationsData] = await Promise.all([
        configsResponse.json(),
        applicationsResponse.json()
      ]);

      if (configsData.success) {
        setLevelConfigs(configsData.data);
      }

      if (applicationsData.success) {
        setApplications(applicationsData.data.applications);
      }

    } catch (error) {
      console.error('加载数据失败:', error);
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitApplication = async () => {
    try {
      setSubmitting(true);
      setError(null);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await fetch('/api/level-application/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || '提交申请失败');
      }

      if (data.success) {
        setSuccessMessage('等级申请提交成功！请等待管理员审核。');
        setShowApplicationForm(false);
        setFormData({
          requestedLevel: '',
          reason: '',
          contactInfo: '',
          businessInfo: '',
          expectedUsage: '',
        });
        // 重新加载申请历史
        loadData();
        setTimeout(() => setSuccessMessage(null), 5000);
      } else {
        throw new Error(data.error?.message || '提交申请失败');
      }

    } catch (error) {
      console.error('提交申请失败:', error);
      setError(error instanceof Error ? error.message : '提交申请失败');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancelApplication = async (applicationId: number) => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) return;

      const response = await fetch(`/api/level-application/${applicationId}/cancel`, {
        method: 'PUT',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        setSuccessMessage('申请已取消');
        loadData();
        setTimeout(() => setSuccessMessage(null), 3000);
      }
    } catch (error) {
      console.error('取消申请失败:', error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      pending: { variant: 'secondary' as const, label: '待审核' },
      approved: { variant: 'success' as const, label: '已批准' },
      rejected: { variant: 'error' as const, label: '已拒绝' },
      cancelled: { variant: 'outline' as const, label: '已取消' },
    };
    
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getLevelDisplayName = (level: string) => {
    const config = levelConfigs.find(c => c.level === level);
    return config?.displayName || level;
  };

  const canApplyForLevel = (targetLevel: string) => {
    const levelOrder = { free: 0, vip1: 1, vip2: 2, vip3: 3 };
    const currentOrder = levelOrder[userProfile.userLevel as keyof typeof levelOrder] || 0;
    const targetOrder = levelOrder[targetLevel as keyof typeof levelOrder] || 0;
    
    // 只能申请更高等级
    return targetOrder > currentOrder;
  };

  const hasPendingApplication = applications.some(app => app.status === 'pending');

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
        <p className="text-gray-600">正在加载等级信息...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 成功消息 */}
      {successMessage && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-600">{successMessage}</p>
        </div>
      )}

      {/* 错误消息 */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* 当前等级信息 */}
      <Card>
        <CardHeader>
          <CardTitle>当前等级</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="secondary" className="text-lg px-3 py-1">
                  {getLevelDisplayName(userProfile.userLevel)}
                </Badge>
              </div>
              {userProfile.levelExpiresAt && (
                <p className="text-sm text-gray-600">
                  过期时间: {formatDate(userProfile.levelExpiresAt)}
                </p>
              )}
            </div>
            {!hasPendingApplication && (
              <Button 
                onClick={() => setShowApplicationForm(true)}
                disabled={userProfile.userLevel === 'vip3'}
              >
                {userProfile.userLevel === 'vip3' ? '已是最高等级' : '申请升级'}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 等级对比表 */}
      <Card>
        <CardHeader>
          <CardTitle>等级对比</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">等级</th>
                  <th className="text-left py-2">每日上传</th>
                  <th className="text-left py-2">文件大小</th>
                  <th className="text-left py-2">存储空间</th>
                  <th className="text-left py-2">选择接口</th>
                  <th className="text-left py-2">优先支持</th>
                  <th className="text-left py-2">自定义域名</th>
                </tr>
              </thead>
              <tbody>
                {levelConfigs.map((config) => (
                  <tr 
                    key={config.level} 
                    className={`border-b ${config.level === userProfile.userLevel ? 'bg-blue-50' : ''}`}
                  >
                    <td className="py-2">
                      <Badge variant={config.level === userProfile.userLevel ? 'primary' : 'outline'}>
                        {config.displayName}
                      </Badge>
                    </td>
                    <td className="py-2">{config.maxDailyUploads}</td>
                    <td className="py-2">{formatFileSize(config.maxFileSize)}</td>
                    <td className="py-2">{formatFileSize(config.maxStorageSpace)}</td>
                    <td className="py-2">{config.canChooseProvider ? '✅' : '❌'}</td>
                    <td className="py-2">{config.prioritySupport ? '✅' : '❌'}</td>
                    <td className="py-2">{config.customDomain ? '✅' : '❌'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* 申请表单 */}
      {showApplicationForm && (
        <Card>
          <CardHeader>
            <CardTitle>申请等级升级</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                申请等级 *
              </label>
              <select
                value={formData.requestedLevel}
                onChange={(e) => setFormData(prev => ({ ...prev, requestedLevel: e.target.value }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">请选择要申请的等级</option>
                {levelConfigs
                  .filter(config => canApplyForLevel(config.level))
                  .map(config => (
                    <option key={config.level} value={config.level}>
                      {config.displayName}
                    </option>
                  ))
                }
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                申请理由 *
              </label>
              <textarea
                value={formData.reason}
                onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={4}
                placeholder="请详细说明您申请升级的理由，包括使用场景、预期需求等..."
                maxLength={1000}
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                {formData.reason.length}/1000 字符
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                联系方式
              </label>
              <input
                type="text"
                value={formData.contactInfo}
                onChange={(e) => setFormData(prev => ({ ...prev, contactInfo: e.target.value }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="QQ、微信、电话等联系方式"
                maxLength={200}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                业务信息
              </label>
              <textarea
                value={formData.businessInfo}
                onChange={(e) => setFormData(prev => ({ ...prev, businessInfo: e.target.value }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="请简要介绍您的业务类型、网站用途等..."
                maxLength={1000}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                预期使用量
              </label>
              <textarea
                value={formData.expectedUsage}
                onChange={(e) => setFormData(prev => ({ ...prev, expectedUsage: e.target.value }))}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={2}
                placeholder="预计每日上传量、存储需求等..."
                maxLength={500}
              />
            </div>

            <div className="flex space-x-3 pt-4">
              <Button
                onClick={handleSubmitApplication}
                disabled={submitting || !formData.requestedLevel || !formData.reason}
              >
                {submitting ? '提交中...' : '提交申请'}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowApplicationForm(false);
                  setFormData({
                    requestedLevel: '',
                    reason: '',
                    contactInfo: '',
                    businessInfo: '',
                    expectedUsage: '',
                  });
                }}
                disabled={submitting}
              >
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 申请历史 */}
      <Card>
        <CardHeader>
          <CardTitle>申请历史</CardTitle>
        </CardHeader>
        <CardContent>
          {applications.length > 0 ? (
            <div className="space-y-4">
              {applications.map((application) => (
                <div key={application.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-medium">
                          {getLevelDisplayName(application.currentLevel)} → {getLevelDisplayName(application.requestedLevel)}
                        </span>
                        {getStatusBadge(application.status)}
                      </div>
                      <p className="text-xs text-gray-500">
                        申请时间: {formatDate(application.createdAt)}
                      </p>
                      {application.processedAt && (
                        <p className="text-xs text-gray-500">
                          处理时间: {formatDate(application.processedAt)}
                          {application.admin && (
                            <span className="ml-2">
                              处理人: {application.admin.displayName || application.admin.username}
                            </span>
                          )}
                        </p>
                      )}
                    </div>
                    {application.status === 'pending' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleCancelApplication(application.id)}
                      >
                        取消申请
                      </Button>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div>
                      <span className="text-xs font-medium text-gray-700">申请理由:</span>
                      <p className="text-xs text-gray-600 mt-1">{application.reason}</p>
                    </div>

                    {application.adminComment && (
                      <div>
                        <span className="text-xs font-medium text-gray-700">管理员备注:</span>
                        <p className="text-xs text-gray-600 mt-1">{application.adminComment}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-sm text-gray-500">暂无申请记录</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
