"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = exports.redis = void 0;
exports.connectRedis = connectRedis;
exports.disconnectRedis = disconnectRedis;
const ioredis_1 = __importDefault(require("ioredis"));
const redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
};
if (process.env.REDIS_PASSWORD) {
    redisConfig.password = process.env.REDIS_PASSWORD;
}
if (process.env.REDIS_TLS === 'true') {
    redisConfig.tls = {};
}
exports.redis = new ioredis_1.default(redisConfig);
exports.redis.on('connect', () => {
    console.log('✅ Redis连接成功');
});
exports.redis.on('error', (error) => {
    console.error('❌ Redis连接错误:', error);
});
exports.redis.on('close', () => {
    console.log('⚠️ Redis连接已关闭');
});
exports.redis.on('reconnecting', () => {
    console.log('🔄 Redis重新连接中...');
});
async function connectRedis() {
    try {
        await exports.redis.ping();
        console.log('✅ Redis连接测试成功');
    }
    catch (error) {
        console.error('❌ Redis连接测试失败:', error);
        throw error;
    }
}
async function disconnectRedis() {
    try {
        await exports.redis.quit();
        console.log('✅ Redis连接已关闭');
    }
    catch (error) {
        console.error('❌ 关闭Redis连接时出错:', error);
    }
}
class RedisService {
    static async set(key, value, ttl) {
        const serializedValue = JSON.stringify(value);
        if (ttl) {
            await exports.redis.setex(key, ttl, serializedValue);
        }
        else {
            await exports.redis.set(key, serializedValue);
        }
    }
    static async get(key) {
        const value = await exports.redis.get(key);
        if (!value)
            return null;
        try {
            return JSON.parse(value);
        }
        catch {
            return value;
        }
    }
    static async del(key) {
        await exports.redis.del(key);
    }
    static async exists(key) {
        const result = await exports.redis.exists(key);
        return result === 1;
    }
    static async expire(key, ttl) {
        await exports.redis.expire(key, ttl);
    }
    static async incr(key) {
        return await exports.redis.incr(key);
    }
    static async decr(key) {
        return await exports.redis.decr(key);
    }
}
exports.RedisService = RedisService;
//# sourceMappingURL=redis.js.map