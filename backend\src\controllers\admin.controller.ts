import { Request, Response } from 'express';
import { prisma } from '../config/database';
import { ApiResponse, ErrorCodes } from '../types';
import { LogService } from '../services/log.service';

export class AdminController {
  /**
   * 获取系统统计数据
   */
  static async getSystemStats(req: Request, res: Response): Promise<void> {
    try {
      // 获取用户统计
      const totalUsers = await prisma.user.count();
      const activeUsers = await prisma.user.count({
        where: {
          status: 'active'
        }
      });

      // 获取上传统计
      const totalUploads = await prisma.userImage.count();
      const todayUploads = await prisma.userImage.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      });

      // 获取存储统计
      const storageStats = await prisma.image.aggregate({
        _sum: {
          fileSize: true
        },
        where: {
          isDeleted: false
        }
      });

      // 模拟系统负载数据（实际项目中应该从系统监控服务获取）
      const systemLoad = {
        cpu: Math.floor(Math.random() * 100),
        memory: Math.floor(Math.random() * 100),
        disk: Math.floor(Math.random() * 100)
      };

      // 获取在线用户数（模拟数据）
      const onlineUsers = Math.floor(Math.random() * 50);

      const stats = {
        totalUsers,
        activeUsers,
        totalUploads,
        todayUploads,
        totalStorage: storageStats._sum.fileSize || 0,
        systemLoad,
        onlineUsers
      };

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取系统统计失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取系统统计失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }



  /**
   * 更新用户等级
   */
  static async updateUserLevel(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { level, expiresAt } = req.body;

      // 验证等级
      const validLevels = ['free', 'vip1', 'vip2', 'vip3'];
      if (!validLevels.includes(level)) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '无效的用户等级',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: Number(id) }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 不能修改管理员的等级
      if (user.role === 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.FORBIDDEN,
            message: '不能修改管理员的等级',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 更新用户等级
      const updatedUser = await prisma.user.update({
        where: { id: Number(id) },
        data: {
          userLevel: level,
          levelExpiresAt: expiresAt ? new Date(expiresAt) : null
        },
        select: {
          id: true,
          username: true,
          email: true,
          userLevel: true,
          levelExpiresAt: true
        }
      });

      res.json({
        success: true,
        data: updatedUser,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('更新用户等级失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新用户等级失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }



  /**
   * 获取用户详情
   */
  static async getUserDetails(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const user = await prisma.user.findUnique({
        where: { id: Number(id) },
        include: {
          userImages: {
            include: {
              image: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 10 // 最近10次上传
          }
        }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: user,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户详情失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户详情失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 获取实时日志（用于初始化连接时获取最近的日志）
  static async getRealtimeLogs(req: Request, res: Response): Promise<void> {
    try {
      const { type = 'all', limit = 100 } = req.query;

      let logs: any[] = [];

      if (type === 'all' || type === 'system') {
        const systemLogs = await LogService.getSystemLogs({
          page: 1,
          limit: parseInt(limit as string) || 100,
        });
        logs = [...logs, ...systemLogs.logs.map(log => ({ ...log, type: 'system' }))];
      }

      if (type === 'all' || type === 'upload') {
        const uploadLogs = await LogService.getUploadLogs({
          page: 1,
          limit: parseInt(limit as string) || 100,
        });
        logs = [...logs, ...uploadLogs.logs.map(log => ({ ...log, type: 'upload' }))];
      }

      // 按时间排序
      logs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      res.json({
        success: true,
        data: logs.slice(0, parseInt(limit as string) || 100),
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取实时日志失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取实时日志失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  static async getSystemLogs(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 50,
        levels,
        types,
        startDate,
        endDate
      } = req.query;

      const filters: any = {};

      if (levels) {
        filters.levels = (levels as string).split(',');
      }

      if (types) {
        filters.types = (types as string).split(',');
      }

      if (startDate) {
        filters.startDate = startDate as string;
      }

      if (endDate) {
        filters.endDate = endDate as string;
      }

      const result = await LogService.getSystemLogs({
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        filters,
      });

      res.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取系统日志失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取系统日志失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 用户管理相关方法

  // 获取用户列表
  static async getUsers(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        status,
        role,
        userLevel,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      const take = parseInt(limit as string);

      // 构建查询条件
      const where: any = {};

      if (search) {
        where.OR = [
          { username: { contains: search as string, mode: 'insensitive' } },
          { email: { contains: search as string, mode: 'insensitive' } },
          { displayName: { contains: search as string, mode: 'insensitive' } }
        ];
      }

      if (status) {
        where.status = status;
      }

      if (role) {
        where.role = role;
      }

      if (userLevel) {
        where.userLevel = userLevel;
      }

      // 构建排序条件
      const orderBy: any = {};
      orderBy[sortBy as string] = sortOrder;

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          orderBy,
          skip,
          take,
          select: {
            id: true,
            username: true,
            email: true,
            displayName: true,
            role: true,
            userLevel: true,
            status: true,
            avatarUrl: true,
            createdAt: true,
            updatedAt: true,
            lastLoginAt: true,
            lastActiveAt: true,
            levelExpiresAt: true,
            _count: {
              select: {
                userImages: true,
                uploadLogs: true
              }
            }
          }
        }),
        prisma.user.count({ where })
      ]);

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户列表失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户列表失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 获取用户详细信息
  static async getUserDetail(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const adminId = (req as any).user.userId;

      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '用户ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        include: {
          userSettings: true,
          _count: {
            select: {
              userImages: true,
              uploadLogs: true,
              activityLogs: true,
              loginHistory: true
            }
          }
        }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.USER_NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminId,
        'view_user_detail',
        'user',
        user.id,
        { viewedUser: user.username },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: user,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户详情失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户详情失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 更新用户状态
  static async updateUserStatus(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { status, reason } = req.body;
      const adminId = (req as any).user.userId;

      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '用户ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const validStatuses = ['active', 'suspended', 'banned'];
      if (!validStatuses.includes(status)) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '无效的用户状态',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: { id: true, username: true, status: true, role: true }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.USER_NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 不能修改其他管理员的状态
      if (user.role === 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.PERMISSION_DENIED,
            message: '不能修改管理员的状态',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const updatedUser = await prisma.user.update({
        where: { id: parseInt(userId) },
        data: {
          status,
          updatedAt: new Date()
        },
        select: {
          id: true,
          username: true,
          status: true,
          updatedAt: true
        }
      });

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminId,
        'update_user_status',
        'user',
        user.id,
        {
          oldStatus: user.status,
          newStatus: status,
          reason,
          targetUser: user.username
        },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: updatedUser,
        message: '用户状态更新成功',
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('更新用户状态失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新用户状态失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  static async getProviders(req: Request, res: Response): Promise<void> {
    // TODO: 实现接口提供商列表获取
    res.json({ success: true, data: [], timestamp: new Date().toISOString() });
  }

  static async createProvider(req: Request, res: Response): Promise<void> {
    // TODO: 实现创建接口提供商
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async updateProvider(req: Request, res: Response): Promise<void> {
    // TODO: 实现更新接口提供商
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async deleteProvider(req: Request, res: Response): Promise<void> {
    // TODO: 实现删除接口提供商
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async getSystemMonitoring(req: Request, res: Response): Promise<void> {
    // TODO: 实现系统监控数据获取
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async getIPSecurity(req: Request, res: Response): Promise<void> {
    // TODO: 实现IP安全数据获取
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async addIPToBlacklist(req: Request, res: Response): Promise<void> {
    // TODO: 实现添加IP到黑名单
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async removeIPFromBlacklist(req: Request, res: Response): Promise<void> {
    // TODO: 实现从黑名单移除IP
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async getAnalyticsOverview(req: Request, res: Response): Promise<void> {
    // TODO: 实现数据分析概览获取
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async getUserAnalytics(req: Request, res: Response): Promise<void> {
    // TODO: 实现用户行为分析获取
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async getUploadAnalytics(req: Request, res: Response): Promise<void> {
    // TODO: 实现上传数据分析获取
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }
}
