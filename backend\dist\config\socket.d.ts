import { Server as HttpServer } from 'http';
export interface ServerToClientEvents {
    'log:system': (log: SystemLogData) => void;
    'log:upload': (log: UploadLogData) => void;
    'log:access': (log: AccessLogData) => void;
    'log:admin': (log: AdminLogData) => void;
    'monitoring:system': (data: SystemMonitoringData) => void;
    'monitoring:alert': (alert: SystemAlert) => void;
    'connection:status': (status: ConnectionStatus) => void;
    'error': (error: SocketError) => void;
}
export interface ClientToServerEvents {
    'logs:subscribe': (filters: LogFilters) => void;
    'logs:unsubscribe': () => void;
    'logs:history': (params: LogHistoryParams, callback: (data: any) => void) => void;
    'monitoring:subscribe': () => void;
    'monitoring:unsubscribe': () => void;
}
export interface InterServerEvents {
    ping: () => void;
}
export interface SocketData {
    userId: number;
    username: string;
    role: string;
    isAdmin: boolean;
}
export interface SystemLogData {
    id: number;
    eventType: string;
    eventLevel: string;
    eventMessage: string;
    eventData?: any;
    serverInstance?: string;
    createdAt: string;
}
export interface UploadLogData {
    id: number;
    userId: number;
    username?: string;
    imageId?: number;
    actionType: string;
    fileName?: string;
    fileSize?: number;
    ipAddress: string;
    userAgent?: string;
    isSuccess: boolean;
    errorMessage?: string;
    createdAt: string;
}
export interface AccessLogData {
    id: number;
    imageId: number;
    ipAddress: string;
    userAgent?: string;
    referer?: string;
    country?: string;
    city?: string;
    responseTime?: number;
    createdAt: string;
}
export interface AdminLogData {
    id: number;
    adminId: number;
    adminUsername?: string;
    operationType: string;
    targetType?: string;
    targetId?: number;
    operationDetails?: any;
    ipAddress?: string;
    userAgent?: string;
    createdAt: string;
}
export interface SystemMonitoringData {
    cpu: number;
    memory: number;
    disk: number;
    network: {
        bytesIn: number;
        bytesOut: number;
    };
    activeConnections: number;
    timestamp: string;
}
export interface SystemAlert {
    type: 'high_cpu' | 'high_memory' | 'high_disk' | 'high_connections' | 'error_rate';
    level: 'warning' | 'critical';
    message: string;
    value: number;
    threshold: number;
    timestamp: string;
}
export interface ConnectionStatus {
    connected: boolean;
    timestamp: string;
    clientsCount: number;
}
export interface SocketError {
    code: string;
    message: string;
    timestamp: string;
}
export interface LogFilters {
    types?: string[];
    levels?: string[];
    startDate?: string;
    endDate?: string;
    userId?: number;
    ipAddress?: string;
}
export interface LogHistoryParams {
    page: number;
    limit: number;
    filters?: LogFilters;
}
export declare class SocketService {
    private static instance;
    private io;
    private connectedAdmins;
    private constructor();
    static getInstance(httpServer?: HttpServer): SocketService;
    private setupMiddleware;
    private setupEventHandlers;
    broadcastSystemLog(log: SystemLogData): void;
    broadcastUploadLog(log: UploadLogData): void;
    broadcastAccessLog(log: AccessLogData): void;
    broadcastAdminLog(log: AdminLogData): void;
    broadcastSystemMonitoring(data: SystemMonitoringData): void;
    broadcastSystemAlert(alert: SystemAlert): void;
    getConnectedAdminsCount(): number;
    close(): void;
}
//# sourceMappingURL=socket.d.ts.map