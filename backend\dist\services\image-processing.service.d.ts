export interface ImageInfo {
    width?: number | undefined;
    height?: number | undefined;
    format?: string | undefined;
    size: number;
    hasAlpha?: boolean | undefined;
    channels?: number | undefined;
    density?: number | undefined;
}
export interface ThumbnailOptions {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'jpeg' | 'png' | 'webp';
    fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
}
export declare class ImageProcessingService {
    static getImageInfo(buffer: Buffer, mimeType: string): Promise<ImageInfo>;
    static generateThumbnail(buffer: Buffer, options?: ThumbnailOptions): Promise<Buffer>;
    static compressImage(buffer: Buffer, quality?: number, format?: 'jpeg' | 'png' | 'webp'): Promise<Buffer>;
    static resizeImage(buffer: Buffer, width?: number, height?: number, options?: {
        fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
        withoutEnlargement?: boolean;
    }): Promise<Buffer>;
    static convertFormat(buffer: Buffer, format: 'jpeg' | 'png' | 'webp', quality?: number): Promise<Buffer>;
    static addWatermark(buffer: Buffer, watermarkBuffer: Buffer, options?: {
        position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
        opacity?: number;
        margin?: number;
    }): Promise<Buffer>;
    static generateMultipleThumbnails(buffer: Buffer, sizes: {
        name: string;
        width: number;
        height: number;
    }[]): Promise<{
        [key: string]: Buffer;
    }>;
    static optimizeImage(buffer: Buffer, targetSizeKB?: number): Promise<{
        buffer: Buffer;
        originalSize: number;
        optimizedSize: number;
        compressionRatio: number;
    }>;
    static isImageMimeType(mimeType: string): boolean;
    static validateImage(buffer: Buffer): Promise<boolean>;
    static getDominantColor(buffer: Buffer): Promise<{
        r: number;
        g: number;
        b: number;
    }>;
    static createMosaic(buffer: Buffer, pixelSize?: number): Promise<Buffer>;
}
//# sourceMappingURL=image-processing.service.d.ts.map