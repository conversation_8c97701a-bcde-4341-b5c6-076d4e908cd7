import Joi from 'joi';

// 通用验证规则
const commonValidation = {
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .max(100)
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'string.max': '邮箱地址不能超过100个字符',
      'any.required': '邮箱地址不能为空',
    }),

  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&\-_+=<>,.;:'"(){}[\]|\\\/~`^#]*$/)
    .required()
    .messages({
      'string.min': '密码至少需要8个字符',
      'string.max': '密码不能超过128个字符',
      'string.pattern.base': '密码必须包含大小写字母和数字',
      'any.required': '密码不能为空',
    }),

  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过30个字符',
      'any.required': '用户名不能为空',
    }),

  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID必须是数字',
      'number.integer': 'ID必须是整数',
      'number.positive': 'ID必须是正数',
      'any.required': 'ID不能为空',
    }),

  uuid: Joi.string()
    .guid({ version: 'uuidv4' })
    .required()
    .messages({
      'string.guid': '无效的UUID格式',
      'any.required': 'UUID不能为空',
    }),
};

// 认证相关验证
export const authValidation = {
  // 用户注册
  register: Joi.object({
    username: commonValidation.username,
    email: commonValidation.email,
    password: commonValidation.password,
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.only': '确认密码必须与密码一致',
        'any.required': '确认密码不能为空',
      }),
    acceptTerms: Joi.boolean()
      .valid(true)
      .required()
      .messages({
        'any.only': '必须同意服务条款',
        'any.required': '必须同意服务条款',
      }),
    acceptPrivacy: Joi.boolean()
      .valid(true)
      .required()
      .messages({
        'any.only': '必须同意隐私政策',
        'any.required': '必须同意隐私政策',
      }),
  }),

  // 用户登录
  login: Joi.object({
    email: commonValidation.email,
    password: Joi.string()
      .required()
      .messages({
        'any.required': '密码不能为空',
      }),
    rememberMe: Joi.boolean().default(false),
  }),

  // 修改密码
  changePassword: Joi.object({
    currentPassword: Joi.string()
      .required()
      .messages({
        'any.required': '当前密码不能为空',
      }),
    newPassword: commonValidation.password,
    confirmPassword: Joi.string()
      .valid(Joi.ref('newPassword'))
      .required()
      .messages({
        'any.only': '确认密码必须与新密码一致',
        'any.required': '确认密码不能为空',
      }),
  }),

  // 忘记密码
  forgotPassword: Joi.object({
    email: commonValidation.email,
  }),

  // 重置密码
  resetPassword: Joi.object({
    token: Joi.string()
      .required()
      .messages({
        'any.required': '重置令牌不能为空',
      }),
    newPassword: commonValidation.password,
    confirmPassword: Joi.string()
      .valid(Joi.ref('newPassword'))
      .required()
      .messages({
        'any.only': '确认密码必须与新密码一致',
        'any.required': '确认密码不能为空',
      }),
  }),
};

// 图片上传相关验证
export const uploadValidation = {
  // 单文件上传
  singleUpload: Joi.object({
    description: Joi.string()
      .max(500)
      .optional()
      .messages({
        'string.max': '描述不能超过500个字符',
      }),
    tags: Joi.array()
      .items(Joi.string().max(50))
      .max(10)
      .optional()
      .messages({
        'array.max': '标签数量不能超过10个',
        'string.max': '单个标签不能超过50个字符',
      }),
    isPrivate: Joi.boolean().default(false),
    expiresAt: Joi.date()
      .greater('now')
      .optional()
      .messages({
        'date.greater': '过期时间必须是未来时间',
      }),
  }),

  // 批量上传
  batchUpload: Joi.object({
    files: Joi.array()
      .items(Joi.object({
        filename: Joi.string().required(),
        description: Joi.string().max(500).optional(),
      }))
      .min(1)
      .max(20)
      .required()
      .messages({
        'array.min': '至少需要上传1个文件',
        'array.max': '一次最多上传20个文件',
        'any.required': '文件列表不能为空',
      }),
    isPrivate: Joi.boolean().default(false),
  }),
};

// 用户管理相关验证
export const userValidation = {
  // 更新用户信息
  updateProfile: Joi.object({
    username: Joi.string()
      .alphanum()
      .min(3)
      .max(30)
      .optional()
      .messages({
        'string.alphanum': '用户名只能包含字母和数字',
        'string.min': '用户名至少需要3个字符',
        'string.max': '用户名不能超过30个字符',
      }),
    avatarUrl: Joi.string()
      .uri()
      .max(500)
      .optional()
      .messages({
        'string.uri': '头像URL格式无效',
        'string.max': 'URL长度不能超过500个字符',
      }),
  }),

  // 管理员更新用户等级
  updateUserLevel: Joi.object({
    userId: commonValidation.id,
    newLevel: Joi.string()
      .valid('free', 'vip1', 'vip2', 'vip3')
      .required()
      .messages({
        'any.only': '用户等级必须是: free, vip1, vip2, vip3 之一',
        'any.required': '新等级不能为空',
      }),
    expiresAt: Joi.date()
      .greater('now')
      .optional()
      .messages({
        'date.greater': '过期时间必须是未来时间',
      }),
    reason: Joi.string()
      .max(200)
      .optional()
      .messages({
        'string.max': '原因说明不能超过200个字符',
      }),
  }),

  // 封禁用户
  banUser: Joi.object({
    userId: commonValidation.id,
    reason: Joi.string()
      .max(500)
      .required()
      .messages({
        'string.max': '封禁原因不能超过500个字符',
        'any.required': '封禁原因不能为空',
      }),
    duration: Joi.number()
      .integer()
      .positive()
      .optional()
      .messages({
        'number.integer': '封禁时长必须是整数',
        'number.positive': '封禁时长必须是正数',
      }),
  }),
};

// 系统管理相关验证
export const adminValidation = {
  // 添加上传接口
  addProvider: Joi.object({
    name: Joi.string()
      .max(100)
      .required()
      .messages({
        'string.max': '接口名称不能超过100个字符',
        'any.required': '接口名称不能为空',
      }),
    description: Joi.string()
      .max(500)
      .optional()
      .messages({
        'string.max': '接口描述不能超过500个字符',
      }),
    endpoint: Joi.string()
      .uri()
      .max(500)
      .required()
      .messages({
        'string.uri': '接口地址格式无效',
        'string.max': '接口地址不能超过500个字符',
        'any.required': '接口地址不能为空',
      }),
    apiKey: Joi.string()
      .max(255)
      .optional()
      .messages({
        'string.max': 'API密钥不能超过255个字符',
      }),
    config: Joi.object().optional(),
    priority: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .default(1)
      .messages({
        'number.integer': '优先级必须是整数',
        'number.min': '优先级最小值为1',
        'number.max': '优先级最大值为100',
      }),
    maxFileSize: Joi.number()
      .integer()
      .positive()
      .default(10485760)
      .messages({
        'number.integer': '最大文件大小必须是整数',
        'number.positive': '最大文件大小必须是正数',
      }),
    supportedFormats: Joi.array()
      .items(Joi.string())
      .min(1)
      .required()
      .messages({
        'array.min': '至少需要支持一种文件格式',
        'any.required': '支持的文件格式不能为空',
      }),
    requiredLevel: Joi.string()
      .valid('free', 'vip1', 'vip2', 'vip3')
      .default('free')
      .messages({
        'any.only': '所需等级必须是: free, vip1, vip2, vip3 之一',
      }),
    isPremium: Joi.boolean().default(false),
    costPerUpload: Joi.number()
      .precision(4)
      .min(0)
      .default(0)
      .messages({
        'number.precision': '上传成本最多4位小数',
        'number.min': '上传成本不能为负数',
      }),
  }),

  // 更新系统配置
  updateConfig: Joi.object({
    key: Joi.string()
      .max(100)
      .required()
      .messages({
        'string.max': '配置键不能超过100个字符',
        'any.required': '配置键不能为空',
      }),
    value: Joi.any().required().messages({
      'any.required': '配置值不能为空',
    }),
    description: Joi.string()
      .max(500)
      .optional()
      .messages({
        'string.max': '配置描述不能超过500个字符',
      }),
  }),
};

// 查询参数验证
export const queryValidation = {
  // 分页查询
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  }),

  // 图片列表查询
  imageList: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().valid('createdAt', 'fileSize', 'accessCount').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
    status: Joi.string().valid('processing', 'completed', 'failed').optional(),
    mimeType: Joi.string().optional(),
    dateFrom: Joi.date().optional(),
    dateTo: Joi.date().optional(),
    search: Joi.string().max(100).optional(),
  }),

  // 用户列表查询
  userList: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().valid('createdAt', 'username', 'userLevel').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
    role: Joi.string().valid('user', 'admin').optional(),
    userLevel: Joi.string().valid('free', 'vip1', 'vip2', 'vip3').optional(),
    status: Joi.string().valid('active', 'suspended', 'banned').optional(),
    search: Joi.string().max(100).optional(),
  }),
};
