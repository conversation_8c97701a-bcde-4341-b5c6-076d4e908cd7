import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>H<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface ActivityLog {
  id: number;
  activityType: string;
  activityData?: any;
  ipAddress?: string;
  location?: string;
  deviceInfo?: any;
  createdAt: string;
}

interface LoginHistory {
  id: number;
  loginType: string;
  isSuccess: boolean;
  failureReason?: string;
  ipAddress: string;
  location?: string;
  deviceInfo?: any;
  createdAt: string;
}

export function ActivityLogs() {
  const [activeTab, setActiveTab] = useState<'activity' | 'login'>('activity');
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [loginHistory, setLoginHistory] = useState<LoginHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  useEffect(() => {
    if (activeTab === 'activity') {
      loadActivityLogs();
    } else {
      loadLoginHistory();
    }
  }, [activeTab, pagination.page]);

  const loadActivityLogs = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await fetch(`/api/user/activity-logs?page=${pagination.page}&limit=${pagination.limit}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取活动日志失败');
      }

      const data = await response.json();
      if (data.success) {
        setActivityLogs(data.data.logs);
        setPagination(prev => ({
          ...prev,
          total: data.data.pagination.total,
          totalPages: data.data.pagination.totalPages,
        }));
      } else {
        throw new Error(data.error?.message || '获取活动日志失败');
      }
    } catch (error) {
      console.error('加载活动日志失败:', error);
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setIsLoading(false);
    }
  };

  const loadLoginHistory = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await fetch(`/api/user/login-history?page=${pagination.page}&limit=${pagination.limit}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取登录历史失败');
      }

      const data = await response.json();
      if (data.success) {
        setLoginHistory(data.data.history);
        setPagination(prev => ({
          ...prev,
          total: data.data.pagination.total,
          totalPages: data.data.pagination.totalPages,
        }));
      } else {
        throw new Error(data.error?.message || '获取登录历史失败');
      }
    } catch (error) {
      console.error('加载登录历史失败:', error);
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setIsLoading(false);
    }
  };

  const getActivityIcon = (activityType: string) => {
    const iconMap: Record<string, string> = {
      login: '🔐',
      logout: '🚪',
      upload: '📤',
      delete: '🗑️',
      settings_change: '⚙️',
      password_change: '🔒',
      profile_update: '👤',
      default: '📝',
    };
    return iconMap[activityType] || iconMap.default;
  };

  const getActivityLabel = (activityType: string) => {
    const labelMap: Record<string, string> = {
      login: '登录',
      logout: '登出',
      upload: '上传文件',
      delete: '删除文件',
      settings_change: '设置更改',
      password_change: '密码修改',
      profile_update: '资料更新',
    };
    return labelMap[activityType] || activityType;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>活动记录</CardTitle>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant={activeTab === 'activity' ? 'default' : 'outline'}
                onClick={() => setActiveTab('activity')}
              >
                活动日志
              </Button>
              <Button
                size="sm"
                variant={activeTab === 'login' ? 'default' : 'outline'}
                onClick={() => setActiveTab('login')}
              >
                登录历史
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="loading-spinner w-6 h-6 mx-auto mb-4" />
              <p className="text-xs text-gray-600">正在加载...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-red-500 text-2xl mb-4">⚠️</div>
              <p className="text-xs text-red-600 mb-4">{error}</p>
              <Button size="sm" onClick={activeTab === 'activity' ? loadActivityLogs : loadLoginHistory}>
                重试
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {activeTab === 'activity' ? (
                activityLogs.length > 0 ? (
                  activityLogs.map((log) => (
                    <div key={log.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <span className="text-lg">{getActivityIcon(log.activityType)}</span>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-xs font-medium text-gray-900">
                            {getActivityLabel(log.activityType)}
                          </h4>
                          <span className="text-xs text-gray-500">
                            {formatDate(log.createdAt)}
                          </span>
                        </div>
                        <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                          {log.ipAddress && (
                            <span>IP: {log.ipAddress}</span>
                          )}
                          {log.location && (
                            <span>位置: {log.location}</span>
                          )}
                        </div>
                        {log.activityData && (
                          <details className="mt-2">
                            <summary className="text-xs text-blue-600 cursor-pointer hover:text-blue-800">
                              查看详细信息
                            </summary>
                            <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                              {JSON.stringify(log.activityData, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-xs text-gray-500">暂无活动记录</p>
                  </div>
                )
              ) : (
                loginHistory.length > 0 ? (
                  loginHistory.map((login) => (
                    <div key={login.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <span className="text-lg">
                        {login.isSuccess ? '✅' : '❌'}
                      </span>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <h4 className="text-xs font-medium text-gray-900">
                              {login.isSuccess ? '登录成功' : '登录失败'}
                            </h4>
                            <Badge variant={login.isSuccess ? 'success' : 'error'}>
                              {login.loginType}
                            </Badge>
                          </div>
                          <span className="text-xs text-gray-500">
                            {formatDate(login.createdAt)}
                          </span>
                        </div>
                        <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                          <span>IP: {login.ipAddress}</span>
                          {login.location && (
                            <span>位置: {login.location}</span>
                          )}
                        </div>
                        {!login.isSuccess && login.failureReason && (
                          <p className="mt-1 text-xs text-red-600">
                            失败原因: {login.failureReason}
                          </p>
                        )}
                        {login.deviceInfo && (
                          <details className="mt-2">
                            <summary className="text-xs text-blue-600 cursor-pointer hover:text-blue-800">
                              查看设备信息
                            </summary>
                            <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                              {JSON.stringify(login.deviceInfo, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-xs text-gray-500">暂无登录记录</p>
                  </div>
                )
              )}

              {/* 分页 */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="text-xs text-gray-500">
                    共 {pagination.total} 条记录，第 {pagination.page} / {pagination.totalPages} 页
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      上一页
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
