import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardT<PERSON>le, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Input } from '../ui/Input';
import { adminService } from '../../services/admin.service';
import type { User } from '../../services/admin.service';

interface UserDetailModalProps {
  userId: number | null;
  isOpen: boolean;
  onClose: () => void;
  onUserUpdated: (user: User) => void;
}

export function UserDetailModal({ userId, isOpen, onClose, onUserUpdated }: UserDetailModalProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    userLevel: '',
    status: '',
    levelExpiresAt: ''
  });

  useEffect(() => {
    if (isOpen && userId) {
      loadUserDetails();
    }
  }, [isOpen, userId]);

  const loadUserDetails = async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      const userDetails = await adminService.getUserDetails(userId);
      setUser(userDetails);
      setEditForm({
        userLevel: userDetails.userLevel,
        status: userDetails.status,
        levelExpiresAt: userDetails.levelExpiresAt || ''
      });
    } catch (error) {
      console.error('加载用户详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // 更新用户等级
      if (editForm.userLevel !== user.userLevel) {
        await adminService.updateUserLevel(
          user.id, 
          editForm.userLevel, 
          editForm.levelExpiresAt || undefined
        );
      }

      // 更新用户状态
      if (editForm.status !== user.status) {
        await adminService.updateUserStatus(user.id, editForm.status);
      }

      // 重新加载用户详情
      await loadUserDetails();
      
      // 通知父组件用户已更新
      if (user) {
        onUserUpdated({
          ...user,
          userLevel: editForm.userLevel,
          status: editForm.status as 'active' | 'suspended' | 'banned',
          levelExpiresAt: editForm.levelExpiresAt || undefined
        });
      }

      setEditing(false);
    } catch (error) {
      console.error('更新用户失败:', error);
      alert('更新用户失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const getLevelBadgeColor = (level: string) => {
    switch (level) {
      case 'free': return 'bg-gray-100 text-gray-800';
      case 'vip1': return 'bg-blue-100 text-blue-800';
      case 'vip2': return 'bg-purple-100 text-purple-800';
      case 'vip3': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'suspended': return 'bg-yellow-100 text-yellow-800';
      case 'banned': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">用户详情</h2>
            <Button variant="outline" onClick={onClose}>
              ✕
            </Button>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : user ? (
            <div className="space-y-6">
              {/* 基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">用户名</label>
                      <p className="text-lg font-semibold">{user.username}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">邮箱</label>
                      <p className="text-lg">{user.email}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">角色</label>
                      <Badge className={user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}>
                        {user.role === 'admin' ? '管理员' : '普通用户'}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">注册时间</label>
                      <p>{formatDate(user.createdAt)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 等级和状态 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex justify-between items-center">
                    等级和状态
                    {!editing && user.role !== 'admin' && (
                      <Button size="sm" onClick={() => setEditing(true)}>
                        编辑
                      </Button>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {editing ? (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">用户等级</label>
                        <select
                          value={editForm.userLevel}
                          onChange={(e) => setEditForm({ ...editForm, userLevel: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="free">免费用户</option>
                          <option value="vip1">VIP1</option>
                          <option value="vip2">VIP2</option>
                          <option value="vip3">VIP3</option>
                        </select>
                      </div>
                      
                      {editForm.userLevel !== 'free' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">等级到期时间</label>
                          <Input
                            type="datetime-local"
                            value={editForm.levelExpiresAt ? new Date(editForm.levelExpiresAt).toISOString().slice(0, 16) : ''}
                            onChange={(e) => setEditForm({ ...editForm, levelExpiresAt: e.target.value ? new Date(e.target.value).toISOString() : '' })}
                          />
                        </div>
                      )}

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">用户状态</label>
                        <select
                          value={editForm.status}
                          onChange={(e) => setEditForm({ ...editForm, status: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="active">正常</option>
                          <option value="suspended">暂停</option>
                          <option value="banned">封禁</option>
                        </select>
                      </div>

                      <div className="flex space-x-2">
                        <Button onClick={handleSave} disabled={loading}>
                          {loading ? '保存中...' : '保存'}
                        </Button>
                        <Button variant="outline" onClick={() => setEditing(false)}>
                          取消
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">用户等级</label>
                        <div className="mt-1">
                          <Badge className={getLevelBadgeColor(user.userLevel)}>
                            {user.userLevel === 'free' ? '免费用户' : user.userLevel.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">用户状态</label>
                        <div className="mt-1">
                          <Badge className={getStatusBadgeColor(user.status)}>
                            {user.status === 'active' ? '正常' : user.status === 'suspended' ? '暂停' : '封禁'}
                          </Badge>
                        </div>
                      </div>
                      {user.levelExpiresAt && (
                        <div className="col-span-2">
                          <label className="text-sm font-medium text-gray-500">等级到期时间</label>
                          <p>{formatDate(user.levelExpiresAt)}</p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 使用统计 */}
              <Card>
                <CardHeader>
                  <CardTitle>使用统计</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">总上传次数</label>
                      <p className="text-2xl font-bold text-blue-600">{user.totalUploads}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">存储使用量</label>
                      <p className="text-2xl font-bold text-green-600">{formatFileSize(user.storageUsed)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">加载用户详情失败</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
