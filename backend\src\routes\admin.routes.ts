import { Router } from 'express';
import { authenticateToken, requireAdmin } from '../middleware/auth.middleware';
import { AdminController } from '../controllers/admin.controller';

const router = Router();

// 所有管理端路由都需要管理员权限
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * @route   GET /api/admin/stats
 * @desc    获取系统统计数据
 * @access  Admin
 */
router.get('/stats', AdminController.getSystemStats);

/**
 * @route   GET /api/admin/users
 * @desc    获取用户列表
 * @access  Admin
 */
router.get('/users', AdminController.getUsers);

/**
 * @route   PUT /api/admin/users/:id/level
 * @desc    更新用户等级
 * @access  Admin
 */
router.put('/users/:id/level', AdminController.updateUserLevel);

/**
 * @route   PUT /api/admin/users/:id/status
 * @desc    更新用户状态
 * @access  Admin
 */
router.put('/users/:id/status', AdminController.updateUserStatus);

/**
 * @route   GET /api/admin/users/:id
 * @desc    获取用户详情
 * @access  Admin
 */
router.get('/users/:id', AdminController.getUserDetails);

/**
 * @route   GET /api/admin/logs/realtime
 * @desc    获取实时日志
 * @access  Admin
 */
router.get('/logs/realtime', AdminController.getRealtimeLogs);

/**
 * @route   GET /api/admin/logs/system
 * @desc    获取系统日志
 * @access  Admin
 */
router.get('/logs/system', AdminController.getSystemLogs);

/**
 * @route   GET /api/admin/providers
 * @desc    获取接口提供商列表
 * @access  Admin
 */
router.get('/providers', AdminController.getProviders);

/**
 * @route   POST /api/admin/providers
 * @desc    添加新的接口提供商
 * @access  Admin
 */
router.post('/providers', AdminController.createProvider);

/**
 * @route   PUT /api/admin/providers/:id
 * @desc    更新接口提供商
 * @access  Admin
 */
router.put('/providers/:id', AdminController.updateProvider);

/**
 * @route   DELETE /api/admin/providers/:id
 * @desc    删除接口提供商
 * @access  Admin
 */
router.delete('/providers/:id', AdminController.deleteProvider);

/**
 * @route   GET /api/admin/monitoring/system
 * @desc    获取系统监控数据
 * @access  Admin
 */
router.get('/monitoring/system', AdminController.getSystemMonitoring);

/**
 * @route   GET /api/admin/security/ips
 * @desc    获取IP安全数据
 * @access  Admin
 */
router.get('/security/ips', AdminController.getIPSecurity);

/**
 * @route   POST /api/admin/security/ips/blacklist
 * @desc    添加IP到黑名单
 * @access  Admin
 */
router.post('/security/ips/blacklist', AdminController.addIPToBlacklist);

/**
 * @route   DELETE /api/admin/security/ips/blacklist/:ip
 * @desc    从黑名单移除IP
 * @access  Admin
 */
router.delete('/security/ips/blacklist/:ip', AdminController.removeIPFromBlacklist);

/**
 * @route   GET /api/admin/analytics/overview
 * @desc    获取数据分析概览
 * @access  Admin
 */
router.get('/analytics/overview', AdminController.getAnalyticsOverview);

/**
 * @route   GET /api/admin/analytics/users
 * @desc    获取用户行为分析
 * @access  Admin
 */
router.get('/analytics/users', AdminController.getUserAnalytics);

/**
 * @route   GET /api/admin/analytics/uploads
 * @desc    获取上传数据分析
 * @access  Admin
 */
router.get('/analytics/uploads', AdminController.getUploadAnalytics);

export default router;
