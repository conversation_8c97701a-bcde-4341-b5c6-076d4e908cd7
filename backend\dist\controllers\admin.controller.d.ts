import { Request, Response } from 'express';
export declare class AdminController {
    static getSystemStats(req: Request, res: Response): Promise<void>;
    static getUsers(req: Request, res: Response): Promise<void>;
    static updateUserLevel(req: Request, res: Response): Promise<void>;
    static updateUserStatus(req: Request, res: Response): Promise<void>;
    static getUserDetails(req: Request, res: Response): Promise<void>;
    static getRealtimeLogs(req: Request, res: Response): Promise<void>;
    static getSystemLogs(req: Request, res: Response): Promise<void>;
    static getProviders(req: Request, res: Response): Promise<void>;
    static createProvider(req: Request, res: Response): Promise<void>;
    static updateProvider(req: Request, res: Response): Promise<void>;
    static deleteProvider(req: Request, res: Response): Promise<void>;
    static getSystemMonitoring(req: Request, res: Response): Promise<void>;
    static getIPSecurity(req: Request, res: Response): Promise<void>;
    static addIPToBlacklist(req: Request, res: Response): Promise<void>;
    static removeIPFromBlacklist(req: Request, res: Response): Promise<void>;
    static getAnalyticsOverview(req: Request, res: Response): Promise<void>;
    static getUserAnalytics(req: Request, res: Response): Promise<void>;
    static getUploadAnalytics(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=admin.controller.d.ts.map