import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { config } from './config/env';
import { connectDatabase, disconnectDatabase } from './config/database';
import { connectRedis, disconnectRedis } from './config/redis';
import authRoutes from './routes/auth.routes';
import uploadRoutes from './routes/upload.routes';
import adminRoutes from './routes/admin.routes';

// 创建Express应用
const app = express();

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" },
  contentSecurityPolicy: false, // 开发环境下禁用CSP
}));

// CORS配置
app.use(cors({
  origin: config.frontend.url,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// 速率限制
if (config.security.enableRateLimiting) {
  const limiter = rateLimit({
    windowMs: config.security.rateLimitWindow,
    max: config.security.rateLimitMax,
    message: {
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: '请求过于频繁，请稍后再试',
        timestamp: new Date().toISOString(),
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  
  app.use('/api/', limiter);
}

// 解析JSON和URL编码的请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.env,
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API路由
app.get('/api', (req, res) => {
  res.json({
    message: 'LoftChat 智能图片上传管理分发系统 API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/auth',
      upload: '/api/upload',
      images: '/api/images',
      admin: '/api/admin',
    }
  });
});

// 注册路由
app.use('/api/auth', authRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/admin', adminRoutes);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: '请求的资源不存在',
      path: req.originalUrl,
      timestamp: new Date().toISOString(),
    }
  });
});

// 全局错误处理中间件
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('❌ 服务器错误:', error);

  // 开发环境下返回详细错误信息
  const isDevelopment = config.env === 'development';
  
  res.status(error.status || 500).json({
    success: false,
    error: {
      code: error.code || 'INTERNAL_ERROR',
      message: error.message || '服务器内部错误',
      timestamp: new Date().toISOString(),
      ...(isDevelopment && { stack: error.stack }),
    }
  });
});

// 启动服务器
async function startServer(): Promise<void> {
  try {
    // 连接数据库
    await connectDatabase();
    
    // 连接Redis
    await connectRedis();
    
    // 启动HTTP服务器
    const server = app.listen(config.port, () => {
      console.log(`🚀 服务器启动成功！`);
      console.log(`📍 地址: http://localhost:${config.port}`);
      console.log(`🌍 环境: ${config.env}`);
      console.log(`📊 健康检查: http://localhost:${config.port}/health`);
      console.log(`🔗 API文档: http://localhost:${config.port}/api`);
    });

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n📡 收到 ${signal} 信号，开始优雅关闭...`);
      
      server.close(async () => {
        console.log('🔌 HTTP服务器已关闭');
        
        try {
          await disconnectDatabase();
          await disconnectRedis();
          console.log('✅ 所有连接已关闭');
          process.exit(0);
        } catch (error) {
          console.error('❌ 关闭连接时出错:', error);
          process.exit(1);
        }
      });
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  console.error('Promise:', promise);
  process.exit(1);
});

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

export { app, startServer };
