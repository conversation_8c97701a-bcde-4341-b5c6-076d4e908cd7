export declare const config: {
    env: any;
    port: any;
    database: {
        url: any;
    };
    redis: {
        host: any;
        port: any;
        password: any;
    };
    jwt: {
        secret: any;
        expiresIn: any;
    };
    upload: {
        dir: any;
        maxFileSize: any;
        allowedMimeTypes: any;
    };
    frontend: {
        url: any;
    };
    monitoring: {
        enableMetrics: any;
        metricsInterval: any;
    };
    security: {
        enableIpWarning: any;
        enableRateLimiting: any;
        rateLimitWindow: any;
        rateLimitMax: any;
    };
    providers: {
        imgur: {
            clientId: any;
        };
        cloudinary: {
            cloudName: any;
            apiKey: any;
            apiSecret: any;
        };
    };
    email: {
        host: any;
        port: any;
        user: any;
        pass: any;
    };
};
//# sourceMappingURL=env.d.ts.map