"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('开始数据库种子数据初始化...');
    const levelConfigs = [
        {
            level: 'free',
            displayName: '免费用户',
            maxDailyUploads: 10,
            maxFileSize: BigInt(5 * 1024 * 1024),
            maxStorageSpace: BigInt(100 * 1024 * 1024),
            canChooseProvider: false,
            prioritySupport: false,
            customDomain: false,
            visibleProviderCount: 2,
        },
        {
            level: 'vip1',
            displayName: 'VIP1用户',
            maxDailyUploads: 50,
            maxFileSize: BigInt(20 * 1024 * 1024),
            maxStorageSpace: BigInt(1024 * 1024 * 1024),
            canChooseProvider: true,
            prioritySupport: false,
            customDomain: false,
            visibleProviderCount: 4,
        },
        {
            level: 'vip2',
            displayName: 'VIP2用户',
            maxDailyUploads: 200,
            maxFileSize: BigInt(50 * 1024 * 1024),
            maxStorageSpace: BigInt(5 * 1024 * 1024 * 1024),
            canChooseProvider: true,
            prioritySupport: true,
            customDomain: false,
            visibleProviderCount: 6,
        },
        {
            level: 'vip3',
            displayName: 'VIP3用户',
            maxDailyUploads: 1000,
            maxFileSize: BigInt(100 * 1024 * 1024),
            maxStorageSpace: BigInt(20 * 1024 * 1024 * 1024),
            canChooseProvider: true,
            prioritySupport: true,
            customDomain: true,
            visibleProviderCount: 10,
        },
    ];
    for (const config of levelConfigs) {
        await prisma.userLevelConfig.upsert({
            where: { level: config.level },
            update: config,
            create: config,
        });
        console.log(`✅ 创建/更新用户等级配置: ${config.displayName}`);
    }
    const providers = [
        {
            name: 'Local Storage',
            description: '本地存储，用于系统内部访问',
            endpoint: 'http://localhost:3000/api/storage',
            status: 'active',
            priority: 1,
            maxFileSize: BigInt(100 * 1024 * 1024),
            supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            requiredLevel: 'free',
            isPremium: false,
            costPerUpload: 0,
        },
        {
            name: 'Imgur',
            description: 'Imgur图片托管服务',
            endpoint: 'https://api.imgur.com/3/image',
            status: 'inactive',
            priority: 2,
            maxFileSize: BigInt(10 * 1024 * 1024),
            supportedFormats: ['image/jpeg', 'image/png', 'image/gif'],
            requiredLevel: 'free',
            isPremium: false,
            costPerUpload: 0,
        },
        {
            name: 'Cloudinary',
            description: 'Cloudinary云端图片服务',
            endpoint: 'https://api.cloudinary.com/v1_1/upload',
            status: 'inactive',
            priority: 3,
            maxFileSize: BigInt(10 * 1024 * 1024),
            supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            requiredLevel: 'vip1',
            isPremium: true,
            costPerUpload: 0.001,
        },
    ];
    for (const provider of providers) {
        const existing = await prisma.uploadProvider.findFirst({
            where: { name: provider.name }
        });
        if (!existing) {
            await prisma.uploadProvider.create({
                data: provider,
            });
            console.log(`✅ 创建/更新上传接口: ${provider.name}`);
        }
        else {
            console.log(`⚠️ 上传接口已存在: ${provider.name}`);
        }
    }
    const visibilityConfigs = [
        { level: 'free', providerId: 1, isVisible: true, displayOrder: 1 },
        { level: 'free', providerId: 2, isVisible: true, displayOrder: 2 },
        { level: 'vip1', providerId: 1, isVisible: true, displayOrder: 1 },
        { level: 'vip1', providerId: 2, isVisible: true, displayOrder: 2 },
        { level: 'vip1', providerId: 3, isVisible: true, displayOrder: 3 },
        { level: 'vip2', providerId: 1, isVisible: true, displayOrder: 1 },
        { level: 'vip2', providerId: 2, isVisible: true, displayOrder: 2 },
        { level: 'vip2', providerId: 3, isVisible: true, displayOrder: 3 },
        { level: 'vip3', providerId: 1, isVisible: true, displayOrder: 1 },
        { level: 'vip3', providerId: 2, isVisible: true, displayOrder: 2 },
        { level: 'vip3', providerId: 3, isVisible: true, displayOrder: 3 },
    ];
    for (const config of visibilityConfigs) {
        await prisma.levelProviderVisibility.upsert({
            where: {
                level_providerId: {
                    level: config.level,
                    providerId: config.providerId,
                }
            },
            update: config,
            create: config,
        });
    }
    console.log('✅ 创建等级接口可见性配置');
    const riskRules = [
        {
            ruleName: '上传频率限制',
            ruleType: 'upload_limit',
            timeWindow: 60,
            maxAttempts: 100,
            blockDuration: 60,
            isActive: true,
        },
        {
            ruleName: '登录频率限制',
            ruleType: 'login_limit',
            timeWindow: 15,
            maxAttempts: 5,
            blockDuration: 30,
            isActive: true,
        },
        {
            ruleName: '可疑活动检测',
            ruleType: 'suspicious_activity',
            timeWindow: 5,
            maxAttempts: 20,
            blockDuration: 120,
            isActive: true,
        },
    ];
    for (const rule of riskRules) {
        const existing = await prisma.ipRiskRule.findFirst({
            where: { ruleName: rule.ruleName }
        });
        if (!existing) {
            await prisma.ipRiskRule.create({
                data: rule,
            });
            console.log(`✅ 创建IP风控规则: ${rule.ruleName}`);
        }
        else {
            console.log(`⚠️ IP风控规则已存在: ${rule.ruleName}`);
        }
    }
    const systemConfigs = [
        {
            key: 'site_name',
            value: 'LoftChat 智能图片上传管理系统',
            description: '网站名称',
        },
        {
            key: 'site_description',
            value: '基于多接口备份的智能图片上传管理分发系统',
            description: '网站描述',
        },
        {
            key: 'max_upload_size',
            value: 104857600,
            description: '最大上传文件大小（字节）',
        },
        {
            key: 'allowed_file_types',
            value: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            description: '允许的文件类型',
        },
        {
            key: 'enable_registration',
            value: true,
            description: '是否允许用户注册',
        },
        {
            key: 'enable_email_verification',
            value: false,
            description: '是否启用邮箱验证',
        },
        {
            key: 'default_user_level',
            value: 'free',
            description: '新用户默认等级',
        },
    ];
    for (const config of systemConfigs) {
        await prisma.systemConfig.upsert({
            where: { key: config.key },
            update: { value: config.value, description: config.description },
            create: config,
        });
        console.log(`✅ 创建系统配置: ${config.key}`);
    }
    const bcrypt = await Promise.resolve().then(() => __importStar(require('bcrypt')));
    const adminPassword = await bcrypt.hash('Admin123', 12);
    await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            username: 'admin',
            email: '<EMAIL>',
            passwordHash: adminPassword,
            role: 'admin',
            userLevel: 'vip3',
            status: 'active',
        },
    });
    console.log('✅ 创建默认管理员用户: <EMAIL> (密码: Admin123)');
    console.log('🎉 数据库种子数据初始化完成！');
}
main()
    .catch((e) => {
    console.error('❌ 数据库种子数据初始化失败:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map