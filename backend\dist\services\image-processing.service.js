"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageProcessingService = void 0;
const sharp_1 = __importDefault(require("sharp"));
class ImageProcessingService {
    static async getImageInfo(buffer, mimeType) {
        try {
            if (!ImageProcessingService.isImageMimeType(mimeType)) {
                return {
                    size: buffer.length,
                };
            }
            const metadata = await (0, sharp_1.default)(buffer).metadata();
            return {
                width: metadata.width,
                height: metadata.height,
                format: metadata.format,
                size: buffer.length,
                hasAlpha: metadata.hasAlpha,
                channels: metadata.channels,
                density: metadata.density,
            };
        }
        catch (error) {
            console.error('获取图片信息失败:', error);
            return {
                size: buffer.length,
            };
        }
    }
    static async generateThumbnail(buffer, options = {}) {
        try {
            const { width = 300, height = 300, quality = 80, format = 'jpeg', fit = 'cover' } = options;
            let sharpInstance = (0, sharp_1.default)(buffer)
                .resize(width, height, { fit });
            switch (format) {
                case 'jpeg':
                    sharpInstance = sharpInstance.jpeg({ quality });
                    break;
                case 'png':
                    sharpInstance = sharpInstance.png({ quality });
                    break;
                case 'webp':
                    sharpInstance = sharpInstance.webp({ quality });
                    break;
            }
            return await sharpInstance.toBuffer();
        }
        catch (error) {
            console.error('生成缩略图失败:', error);
            throw new Error('生成缩略图失败');
        }
    }
    static async compressImage(buffer, quality = 80, format) {
        try {
            let sharpInstance = (0, sharp_1.default)(buffer);
            if (format) {
                switch (format) {
                    case 'jpeg':
                        sharpInstance = sharpInstance.jpeg({ quality });
                        break;
                    case 'png':
                        sharpInstance = sharpInstance.png({ quality });
                        break;
                    case 'webp':
                        sharpInstance = sharpInstance.webp({ quality });
                        break;
                }
            }
            else {
                const metadata = await (0, sharp_1.default)(buffer).metadata();
                if (metadata.format === 'png' && !metadata.hasAlpha) {
                    sharpInstance = sharpInstance.jpeg({ quality });
                }
                else if (metadata.format === 'png') {
                    sharpInstance = sharpInstance.png({ quality });
                }
                else {
                    sharpInstance = sharpInstance.jpeg({ quality });
                }
            }
            return await sharpInstance.toBuffer();
        }
        catch (error) {
            console.error('压缩图片失败:', error);
            throw new Error('压缩图片失败');
        }
    }
    static async resizeImage(buffer, width, height, options = {}) {
        try {
            const { fit = 'cover', withoutEnlargement = true } = options;
            return await (0, sharp_1.default)(buffer)
                .resize(width, height, { fit, withoutEnlargement })
                .toBuffer();
        }
        catch (error) {
            console.error('调整图片大小失败:', error);
            throw new Error('调整图片大小失败');
        }
    }
    static async convertFormat(buffer, format, quality = 80) {
        try {
            let sharpInstance = (0, sharp_1.default)(buffer);
            switch (format) {
                case 'jpeg':
                    sharpInstance = sharpInstance.jpeg({ quality });
                    break;
                case 'png':
                    sharpInstance = sharpInstance.png({ quality });
                    break;
                case 'webp':
                    sharpInstance = sharpInstance.webp({ quality });
                    break;
            }
            return await sharpInstance.toBuffer();
        }
        catch (error) {
            console.error('转换图片格式失败:', error);
            throw new Error('转换图片格式失败');
        }
    }
    static async addWatermark(buffer, watermarkBuffer, options = {}) {
        try {
            const { position = 'bottom-right', opacity = 0.5, margin = 10 } = options;
            const image = (0, sharp_1.default)(buffer);
            const { width, height } = await image.metadata();
            if (!width || !height) {
                throw new Error('无法获取图片尺寸');
            }
            const watermark = await (0, sharp_1.default)(watermarkBuffer)
                .composite([{
                    input: Buffer.from([255, 255, 255, Math.round(opacity * 255)]),
                    raw: { width: 1, height: 1, channels: 4 },
                    tile: true,
                    blend: 'dest-in'
                }])
                .toBuffer();
            const watermarkMeta = await (0, sharp_1.default)(watermark).metadata();
            const watermarkWidth = watermarkMeta.width || 0;
            const watermarkHeight = watermarkMeta.height || 0;
            let left = 0;
            let top = 0;
            switch (position) {
                case 'top-left':
                    left = margin;
                    top = margin;
                    break;
                case 'top-right':
                    left = width - watermarkWidth - margin;
                    top = margin;
                    break;
                case 'bottom-left':
                    left = margin;
                    top = height - watermarkHeight - margin;
                    break;
                case 'bottom-right':
                    left = width - watermarkWidth - margin;
                    top = height - watermarkHeight - margin;
                    break;
                case 'center':
                    left = Math.round((width - watermarkWidth) / 2);
                    top = Math.round((height - watermarkHeight) / 2);
                    break;
            }
            return await image
                .composite([{
                    input: watermark,
                    left,
                    top,
                }])
                .toBuffer();
        }
        catch (error) {
            console.error('添加水印失败:', error);
            throw new Error('添加水印失败');
        }
    }
    static async generateMultipleThumbnails(buffer, sizes) {
        const thumbnails = {};
        try {
            for (const size of sizes) {
                thumbnails[size.name] = await ImageProcessingService.generateThumbnail(buffer, {
                    width: size.width,
                    height: size.height,
                });
            }
            return thumbnails;
        }
        catch (error) {
            console.error('生成多种缩略图失败:', error);
            throw new Error('生成多种缩略图失败');
        }
    }
    static async optimizeImage(buffer, targetSizeKB) {
        try {
            const originalSize = buffer.length;
            let optimizedBuffer = buffer;
            let quality = 90;
            if (targetSizeKB) {
                const targetSize = targetSizeKB * 1024;
                while (optimizedBuffer.length > targetSize && quality > 10) {
                    optimizedBuffer = await ImageProcessingService.compressImage(buffer, quality);
                    quality -= 10;
                }
            }
            else {
                optimizedBuffer = await ImageProcessingService.compressImage(buffer, 85);
            }
            const optimizedSize = optimizedBuffer.length;
            const compressionRatio = ((originalSize - optimizedSize) / originalSize) * 100;
            return {
                buffer: optimizedBuffer,
                originalSize,
                optimizedSize,
                compressionRatio,
            };
        }
        catch (error) {
            console.error('优化图片失败:', error);
            throw new Error('优化图片失败');
        }
    }
    static isImageMimeType(mimeType) {
        const imageMimeTypes = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/bmp',
            'image/svg+xml',
            'image/x-icon',
        ];
        return imageMimeTypes.includes(mimeType);
    }
    static async validateImage(buffer) {
        try {
            await (0, sharp_1.default)(buffer).metadata();
            return true;
        }
        catch {
            return false;
        }
    }
    static async getDominantColor(buffer) {
        try {
            const { dominant } = await (0, sharp_1.default)(buffer).stats();
            return {
                r: Math.round(dominant.r),
                g: Math.round(dominant.g),
                b: Math.round(dominant.b),
            };
        }
        catch (error) {
            console.error('获取图片主色调失败:', error);
            return { r: 128, g: 128, b: 128 };
        }
    }
    static async createMosaic(buffer, pixelSize = 10) {
        try {
            const { width, height } = await (0, sharp_1.default)(buffer).metadata();
            if (!width || !height) {
                throw new Error('无法获取图片尺寸');
            }
            const smallWidth = Math.ceil(width / pixelSize);
            const smallHeight = Math.ceil(height / pixelSize);
            return await (0, sharp_1.default)(buffer)
                .resize(smallWidth, smallHeight, { kernel: 'nearest' })
                .resize(width, height, { kernel: 'nearest' })
                .toBuffer();
        }
        catch (error) {
            console.error('创建马赛克效果失败:', error);
            throw new Error('创建马赛克效果失败');
        }
    }
}
exports.ImageProcessingService = ImageProcessingService;
//# sourceMappingURL=image-processing.service.js.map