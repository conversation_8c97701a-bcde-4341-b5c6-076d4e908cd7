"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorCodes = void 0;
var ErrorCodes;
(function (ErrorCodes) {
    ErrorCodes["UNAUTHORIZED"] = "AUTH_001";
    ErrorCodes["INVALID_TOKEN"] = "AUTH_002";
    ErrorCodes["TOKEN_EXPIRED"] = "AUTH_003";
    ErrorCodes["INVALID_INPUT"] = "AUTH_004";
    ErrorCodes["FORBIDDEN"] = "PERM_001";
    ErrorCodes["INSUFFICIENT_LEVEL"] = "PERM_002";
    ErrorCodes["FILE_TOO_LARGE"] = "UPLOAD_001";
    ErrorCodes["INVALID_FILE_TYPE"] = "UPLOAD_002";
    ErrorCodes["UPLOAD_LIMIT_EXCEEDED"] = "UPLOAD_003";
    ErrorCodes["STORAGE_FULL"] = "UPLOAD_004";
    ErrorCodes["INVALID_FILE"] = "UPLOAD_005";
    ErrorCodes["IP_BLOCKED"] = "SECURITY_001";
    ErrorCodes["SUSPICIOUS_ACTIVITY"] = "SECURITY_002";
    ErrorCodes["NOT_FOUND"] = "RESOURCE_001";
    ErrorCodes["INTERNAL_ERROR"] = "SYS_001";
    ErrorCodes["DATABASE_ERROR"] = "SYS_002";
    ErrorCodes["EXTERNAL_SERVICE_ERROR"] = "SYS_003";
})(ErrorCodes || (exports.ErrorCodes = ErrorCodes = {}));
//# sourceMappingURL=index.js.map