"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
exports.connectDatabase = connectDatabase;
exports.disconnectDatabase = disconnectDatabase;
const client_1 = require("@prisma/client");
const prisma = globalThis.__prisma || new client_1.PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});
exports.prisma = prisma;
if (process.env.NODE_ENV === 'development') {
    globalThis.__prisma = prisma;
}
async function connectDatabase() {
    try {
        await prisma.$connect();
        console.log('✅ 数据库连接成功');
    }
    catch (error) {
        console.error('❌ 数据库连接失败:', error);
        process.exit(1);
    }
}
async function disconnectDatabase() {
    try {
        await prisma.$disconnect();
        console.log('✅ 数据库连接已关闭');
    }
    catch (error) {
        console.error('❌ 关闭数据库连接时出错:', error);
    }
}
//# sourceMappingURL=database.js.map