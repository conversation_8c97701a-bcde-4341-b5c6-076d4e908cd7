// 通用响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId?: string;
  };
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 用户相关类型
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  role: string;
  userLevel: string;
  status: string;
  avatarUrl?: string | undefined;
  levelExpiresAt?: string | undefined;
  createdAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: UserInfo;
  ipWarning?: {
    isNewIP: boolean;
    location: string;
    lastLogin: string;
  };
}

// 图片相关类型
export interface ImageInfo {
  id: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  width?: number;
  height?: number;
  systemUrl: string;
  uploadStatus: string;
  isOriginalUploader: boolean;
  accessCount: number;
  createdAt: string;
}

export interface UploadResponse {
  success: boolean;
  message: string;
  data?: {
    imageId: string;
    systemUrl: string;
    isReused: boolean;
    originalUploader?: string;
    uploadTime: string;
    fileSize: number;
    mimeType: string;
  };
}

export interface ImageLink {
  id: string;
  externalUrl: string;
  provider: {
    id: string;
    name: string;
    description?: string;
    isPremium: boolean;
  };
  status: string;
  responseTime?: number;
  lastChecked?: string;
}

// 接口提供商类型
export interface Provider {
  id: string;
  name: string;
  description?: string;
  endpoint: string;
  status: string;
  priority: number;
  maxFileSize: number;
  supportedFormats: string[];
  requiredLevel: string;
  isPremium: boolean;
  costPerUpload: number;
}

// 系统监控类型
export interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkIO: number;
  dailyUploads: number;
  activeUsers: number;
  storageUsed: number;
  uploadSuccessRate: number;
  avgResponseTime: number;
  providerAvailability: number;
  blockedIPs: number;
  suspiciousActivities: number;
}

// 错误代码枚举
export enum ErrorCodes {
  // 认证错误
  UNAUTHORIZED = 'AUTH_001',
  INVALID_TOKEN = 'AUTH_002',
  TOKEN_EXPIRED = 'AUTH_003',
  INVALID_INPUT = 'AUTH_004',

  // 权限错误
  FORBIDDEN = 'PERM_001',
  INSUFFICIENT_LEVEL = 'PERM_002',

  // 上传错误
  FILE_TOO_LARGE = 'UPLOAD_001',
  INVALID_FILE_TYPE = 'UPLOAD_002',
  UPLOAD_LIMIT_EXCEEDED = 'UPLOAD_003',
  STORAGE_FULL = 'UPLOAD_004',
  INVALID_FILE = 'UPLOAD_005',

  // IP安全错误
  IP_BLOCKED = 'SECURITY_001',
  SUSPICIOUS_ACTIVITY = 'SECURITY_002',

  // 资源错误
  NOT_FOUND = 'RESOURCE_001',

  // 系统错误
  INTERNAL_ERROR = 'SYS_001',
  DATABASE_ERROR = 'SYS_002',
  EXTERNAL_SERVICE_ERROR = 'SYS_003'
}

// Express Request扩展
declare global {
  namespace Express {
    interface Request {
      user?: UserInfo;
      requestId?: string;
    }
  }
}
