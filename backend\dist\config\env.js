"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const joi_1 = __importDefault(require("joi"));
dotenv_1.default.config();
const envSchema = joi_1.default.object({
    NODE_ENV: joi_1.default.string().valid('development', 'production', 'test').default('development'),
    PORT: joi_1.default.number().default(3000),
    DATABASE_URL: joi_1.default.string().required(),
    REDIS_HOST: joi_1.default.string().default('localhost'),
    REDIS_PORT: joi_1.default.number().default(6379),
    REDIS_PASSWORD: joi_1.default.string().optional(),
    JWT_SECRET: joi_1.default.string().required(),
    JWT_EXPIRES_IN: joi_1.default.string().default('7d'),
    UPLOAD_DIR: joi_1.default.string().default('./uploads'),
    MAX_FILE_SIZE: joi_1.default.number().default(104857600),
    ALLOWED_MIME_TYPES: joi_1.default.string().default('image/jpeg,image/png,image/gif,image/webp'),
    FRONTEND_URL: joi_1.default.string().default('http://localhost:5173'),
    ENABLE_METRICS: joi_1.default.boolean().default(true),
    METRICS_INTERVAL: joi_1.default.number().default(30000),
    ENABLE_IP_WARNING: joi_1.default.boolean().default(true),
    ENABLE_RATE_LIMITING: joi_1.default.boolean().default(true),
    RATE_LIMIT_WINDOW: joi_1.default.number().default(900000),
    RATE_LIMIT_MAX: joi_1.default.number().default(100),
    IMGUR_CLIENT_ID: joi_1.default.string().allow('').optional(),
    CLOUDINARY_CLOUD_NAME: joi_1.default.string().allow('').optional(),
    CLOUDINARY_API_KEY: joi_1.default.string().allow('').optional(),
    CLOUDINARY_API_SECRET: joi_1.default.string().allow('').optional(),
    SMTP_HOST: joi_1.default.string().allow('').optional(),
    SMTP_PORT: joi_1.default.alternatives().try(joi_1.default.number(), joi_1.default.string().allow('')).optional(),
    SMTP_USER: joi_1.default.string().allow('').optional(),
    SMTP_PASS: joi_1.default.string().allow('').optional(),
}).unknown();
const { error, value: envVars } = envSchema.validate(process.env);
if (error) {
    throw new Error(`环境变量配置错误: ${error.message}`);
}
exports.config = {
    env: envVars.NODE_ENV,
    port: envVars.PORT,
    database: {
        url: envVars.DATABASE_URL,
    },
    redis: {
        host: envVars.REDIS_HOST,
        port: envVars.REDIS_PORT,
        password: envVars.REDIS_PASSWORD,
    },
    jwt: {
        secret: envVars.JWT_SECRET,
        expiresIn: envVars.JWT_EXPIRES_IN,
    },
    upload: {
        dir: envVars.UPLOAD_DIR,
        maxFileSize: envVars.MAX_FILE_SIZE,
        allowedMimeTypes: envVars.ALLOWED_MIME_TYPES.split(','),
    },
    frontend: {
        url: envVars.FRONTEND_URL,
    },
    monitoring: {
        enableMetrics: envVars.ENABLE_METRICS,
        metricsInterval: envVars.METRICS_INTERVAL,
    },
    security: {
        enableIpWarning: envVars.ENABLE_IP_WARNING,
        enableRateLimiting: envVars.ENABLE_RATE_LIMITING,
        rateLimitWindow: envVars.RATE_LIMIT_WINDOW,
        rateLimitMax: envVars.RATE_LIMIT_MAX,
    },
    providers: {
        imgur: {
            clientId: envVars.IMGUR_CLIENT_ID,
        },
        cloudinary: {
            cloudName: envVars.CLOUDINARY_CLOUD_NAME,
            apiKey: envVars.CLOUDINARY_API_KEY,
            apiSecret: envVars.CLOUDINARY_API_SECRET,
        },
    },
    email: {
        host: envVars.SMTP_HOST,
        port: envVars.SMTP_PORT,
        user: envVars.SMTP_USER,
        pass: envVars.SMTP_PASS,
    },
};
if (exports.config.env === 'development') {
    console.log('📋 当前配置:', {
        env: exports.config.env,
        port: exports.config.port,
        redis: `${exports.config.redis.host}:${exports.config.redis.port}`,
        uploadDir: exports.config.upload.dir,
        maxFileSize: `${Math.round(exports.config.upload.maxFileSize / 1024 / 1024)}MB`,
    });
}
//# sourceMappingURL=env.js.map