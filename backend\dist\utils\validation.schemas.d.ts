import Joi from 'joi';
export declare const authValidation: {
    register: Joi.ObjectSchema<any>;
    login: Joi.ObjectSchema<any>;
    changePassword: Joi.ObjectSchema<any>;
    forgotPassword: Joi.ObjectSchema<any>;
    resetPassword: Joi.ObjectSchema<any>;
};
export declare const uploadValidation: {
    singleUpload: Joi.ObjectSchema<any>;
    batchUpload: Joi.ObjectSchema<any>;
};
export declare const userValidation: {
    updateProfile: Joi.ObjectSchema<any>;
    updateUserLevel: Joi.ObjectSchema<any>;
    banUser: Joi.ObjectSchema<any>;
};
export declare const adminValidation: {
    addProvider: Joi.ObjectSchema<any>;
    updateConfig: Joi.ObjectSchema<any>;
};
export declare const queryValidation: {
    pagination: Joi.ObjectSchema<any>;
    imageList: Joi.ObjectSchema<any>;
    userList: Joi.ObjectSchema<any>;
};
//# sourceMappingURL=validation.schemas.d.ts.map