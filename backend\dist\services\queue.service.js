"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueService = void 0;
const bullmq_1 = require("bullmq");
const redis_1 = require("../config/redis");
const upload_provider_service_1 = require("./upload-provider.service");
const file_service_1 = require("./file.service");
const database_1 = require("../config/database");
class QueueService {
    static async initialize() {
        try {
            QueueService.uploadQueue = new bullmq_1.Queue('upload-queue', {
                connection: redis_1.redis,
                defaultJobOptions: {
                    removeOnComplete: 100,
                    removeOnFail: 50,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
            });
            QueueService.imageProcessingQueue = new bullmq_1.Queue('image-processing-queue', {
                connection: redis_1.redis,
                defaultJobOptions: {
                    removeOnComplete: 50,
                    removeOnFail: 25,
                    attempts: 2,
                    backoff: {
                        type: 'exponential',
                        delay: 1000,
                    },
                },
            });
            QueueService.uploadWorker = new bullmq_1.Worker('upload-queue', QueueService.processUploadJob, {
                connection: redis_1.redis,
                concurrency: 5,
            });
            QueueService.imageProcessingWorker = new bullmq_1.Worker('image-processing-queue', QueueService.processImageProcessingJob, {
                connection: redis_1.redis,
                concurrency: 3,
            });
            QueueService.setupEventListeners();
            console.log('✅ 队列系统初始化成功');
        }
        catch (error) {
            console.error('❌ 队列系统初始化失败:', error);
            throw error;
        }
    }
    static async addUploadJob(data) {
        try {
            return await QueueService.uploadQueue.add('upload-file', data, {
                priority: 1,
                delay: 0,
            });
        }
        catch (error) {
            console.error('添加上传任务失败:', error);
            throw error;
        }
    }
    static async addImageProcessingJob(data) {
        try {
            return await QueueService.imageProcessingQueue.add('process-image', data, {
                priority: 2,
                delay: 1000,
            });
        }
        catch (error) {
            console.error('添加图片处理任务失败:', error);
            throw error;
        }
    }
    static async processUploadJob(job) {
        const { imageId, userId, filePath, fileInfo } = job.data;
        try {
            console.log(`开始处理上传任务: 图片ID ${imageId}`);
            await job.updateProgress(10);
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: { userLevel: true }
            });
            if (!user) {
                throw new Error('用户不存在');
            }
            await job.updateProgress(20);
            const providers = await upload_provider_service_1.UploadProviderService.getAvailableProviders(userId, user.userLevel);
            if (providers.length === 0) {
                throw new Error('没有可用的上传接口');
            }
            await job.updateProgress(30);
            const fileBuffer = await file_service_1.FileService.readFile(filePath);
            await job.updateProgress(40);
            const uploadResults = await upload_provider_service_1.UploadProviderService.uploadToMultipleProviders(providers, fileBuffer, fileInfo.originalName, fileInfo.mimeType);
            await job.updateProgress(80);
            await upload_provider_service_1.UploadProviderService.saveUploadResults(imageId, uploadResults);
            await job.updateProgress(90);
            const successfulUploads = uploadResults.filter(result => result.success);
            console.log(`图片 ${imageId} 上传完成: ${successfulUploads.length}/${uploadResults.length} 个接口成功`);
            await QueueService.addImageProcessingJob({
                imageId,
                filePath,
                operations: {
                    generateThumbnails: true,
                    compress: true,
                    watermark: false,
                }
            });
            await job.updateProgress(100);
        }
        catch (error) {
            console.error(`上传任务失败 (图片ID: ${imageId}):`, error);
            await database_1.prisma.image.update({
                where: { id: imageId },
                data: { uploadStatus: 'failed' }
            });
            throw error;
        }
    }
    static async processImageProcessingJob(job) {
        const { imageId, filePath, operations } = job.data;
        try {
            console.log(`开始处理图片处理任务: 图片ID ${imageId}`);
            await job.updateProgress(10);
            const fileExists = await file_service_1.FileService.fileExists(filePath);
            if (!fileExists) {
                throw new Error('源文件不存在');
            }
            const fileBuffer = await file_service_1.FileService.readFile(filePath);
            await job.updateProgress(30);
            if (operations.generateThumbnails) {
                await QueueService.generateThumbnails(imageId, fileBuffer);
                await job.updateProgress(60);
            }
            if (operations.compress) {
                await QueueService.compressImage(imageId, fileBuffer);
                await job.updateProgress(80);
            }
            if (operations.watermark) {
                await QueueService.addWatermark(imageId, fileBuffer);
                await job.updateProgress(90);
            }
            console.log(`图片处理完成: 图片ID ${imageId}`);
            await job.updateProgress(100);
        }
        catch (error) {
            console.error(`图片处理任务失败 (图片ID: ${imageId}):`, error);
            throw error;
        }
    }
    static async generateThumbnails(imageId, fileBuffer) {
        try {
            const { ImageProcessingService } = await Promise.resolve().then(() => __importStar(require('./image-processing.service')));
            const thumbnailSizes = [
                { name: 'small', width: 150, height: 150 },
                { name: 'medium', width: 300, height: 300 },
                { name: 'large', width: 600, height: 600 },
            ];
            const thumbnails = await ImageProcessingService.generateMultipleThumbnails(fileBuffer, thumbnailSizes);
            for (const [size, buffer] of Object.entries(thumbnails)) {
                const thumbnailPath = `thumbnails/${imageId}_${size}.jpg`;
                await file_service_1.FileService.saveFile(buffer, thumbnailPath);
            }
            console.log(`缩略图生成完成: 图片ID ${imageId}`);
        }
        catch (error) {
            console.error(`生成缩略图失败 (图片ID: ${imageId}):`, error);
        }
    }
    static async compressImage(imageId, fileBuffer) {
        try {
            const { ImageProcessingService } = await Promise.resolve().then(() => __importStar(require('./image-processing.service')));
            const optimized = await ImageProcessingService.optimizeImage(fileBuffer);
            if (optimized.compressionRatio > 10) {
                const compressedPath = `compressed/${imageId}_compressed.jpg`;
                await file_service_1.FileService.saveFile(optimized.buffer, compressedPath);
                console.log(`图片压缩完成: 图片ID ${imageId}, 压缩率: ${optimized.compressionRatio.toFixed(2)}%`);
            }
        }
        catch (error) {
            console.error(`压缩图片失败 (图片ID: ${imageId}):`, error);
        }
    }
    static async addWatermark(imageId, fileBuffer) {
        try {
            console.log(`水印添加完成: 图片ID ${imageId}`);
        }
        catch (error) {
            console.error(`添加水印失败 (图片ID: ${imageId}):`, error);
        }
    }
    static setupEventListeners() {
        QueueService.uploadWorker.on('completed', (job) => {
            console.log(`上传任务完成: ${job.id}`);
        });
        QueueService.uploadWorker.on('failed', (job, err) => {
            console.error(`上传任务失败: ${job?.id}`, err);
        });
        QueueService.uploadWorker.on('progress', (job, progress) => {
            console.log(`上传任务进度: ${job.id} - ${progress}%`);
        });
        QueueService.imageProcessingWorker.on('completed', (job) => {
            console.log(`图片处理任务完成: ${job.id}`);
        });
        QueueService.imageProcessingWorker.on('failed', (job, err) => {
            console.error(`图片处理任务失败: ${job?.id}`, err);
        });
        QueueService.imageProcessingWorker.on('progress', (job, progress) => {
            console.log(`图片处理任务进度: ${job.id} - ${progress}%`);
        });
    }
    static async getQueueStats() {
        try {
            const uploadStats = await QueueService.uploadQueue.getJobCounts();
            const imageProcessingStats = await QueueService.imageProcessingQueue.getJobCounts();
            return {
                upload: uploadStats,
                imageProcessing: imageProcessingStats,
            };
        }
        catch (error) {
            console.error('获取队列状态失败:', error);
            return {
                upload: {},
                imageProcessing: {},
            };
        }
    }
    static async cleanQueues() {
        try {
            await QueueService.uploadQueue.clean(24 * 60 * 60 * 1000, 100);
            await QueueService.imageProcessingQueue.clean(24 * 60 * 60 * 1000, 100);
            console.log('队列清理完成');
        }
        catch (error) {
            console.error('清理队列失败:', error);
        }
    }
    static async shutdown() {
        try {
            await QueueService.uploadWorker.close();
            await QueueService.imageProcessingWorker.close();
            await QueueService.uploadQueue.close();
            await QueueService.imageProcessingQueue.close();
            console.log('队列系统已关闭');
        }
        catch (error) {
            console.error('关闭队列系统失败:', error);
        }
    }
}
exports.QueueService = QueueService;
//# sourceMappingURL=queue.service.js.map