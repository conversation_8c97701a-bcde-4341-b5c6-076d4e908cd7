import { Request, Response, NextFunction } from 'express';
export declare class UploadController {
    static uploadSingle(req: Request, res: Response, next: NextFunction): Promise<void>;
    static uploadBatch(req: Request, res: Response, next: NextFunction): Promise<void>;
    private static checkUploadLimits;
    private static logUpload;
    static getUploadLimits(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getUploadHistory(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getUploadStats(req: Request, res: Response, next: NextFunction): Promise<void>;
    static deleteUpload(req: Request, res: Response, next: NextFunction): Promise<void>;
    static retryUpload(req: Request, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=upload.controller.d.ts.map