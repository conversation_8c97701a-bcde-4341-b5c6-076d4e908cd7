import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { formatDate } from '../../lib/utils';

export function UserSettings() {
  const { user } = useAuth();
  const [activeSection, setActiveSection] = useState<'profile' | 'security' | 'preferences'>('profile');

  const getUserLevelInfo = (level: string) => {
    const levelMap = {
      free: { 
        label: '免费用户', 
        variant: 'secondary' as const,
        description: '基础功能，每日10次上传',
        features: ['基础上传功能', '2个上传接口', '5MB文件限制', '100MB存储空间']
      },
      vip1: { 
        label: 'VIP1', 
        variant: 'default' as const,
        description: '进阶功能，每日50次上传',
        features: ['选择上传接口', '4个上传接口', '20MB文件限制', '1GB存储空间']
      },
      vip2: { 
        label: 'VIP2', 
        variant: 'warning' as const,
        description: '高级功能，每日200次上传',
        features: ['优先技术支持', '6个上传接口', '50MB文件限制', '5GB存储空间']
      },
      vip3: { 
        label: 'VIP3', 
        variant: 'success' as const,
        description: '专业功能，每日1000次上传',
        features: ['自定义域名', '10个上传接口', '100MB文件限制', '20GB存储空间']
      },
    };
    
    return levelMap[level as keyof typeof levelMap] || { 
      label: level, 
      variant: 'secondary' as const,
      description: '未知等级',
      features: []
    };
  };

  const levelInfo = getUserLevelInfo(user?.userLevel || '');

  return (
    <div className="space-y-6">
      {/* 导航标签 */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { key: 'profile', label: '个人资料' },
            { key: 'security', label: '安全设置' },
            { key: 'preferences', label: '偏好设置' },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveSection(tab.key as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeSection === tab.key
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 个人资料 */}
      {activeSection === 'profile' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="用户名"
                value={user?.username || ''}
                disabled
                helperText="用户名暂不支持修改"
              />
              <Input
                label="邮箱地址"
                type="email"
                value={user?.email || ''}
                disabled
                helperText="邮箱地址暂不支持修改"
              />
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">账号状态</label>
                <div>
                  <Badge variant={user?.status === 'active' ? 'success' : 'error'}>
                    {user?.status === 'active' ? '正常' : '异常'}
                  </Badge>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">注册时间</label>
                <p className="text-sm text-gray-600">
                  {user?.createdAt ? formatDate(user.createdAt) : '未知'}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>会员等级</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Badge variant={levelInfo.variant} className="text-lg px-4 py-2">
                  {levelInfo.label}
                </Badge>
                {user?.role === 'admin' && (
                  <Badge variant="error">管理员</Badge>
                )}
              </div>
              
              <p className="text-sm text-gray-600">
                {levelInfo.description}
              </p>

              {user?.levelExpiresAt && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">到期时间</label>
                  <p className="text-sm text-gray-600">
                    {formatDate(user.levelExpiresAt)}
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">等级特权</label>
                <ul className="text-sm text-gray-600 space-y-1">
                  {levelInfo.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <svg className="w-4 h-4 text-success-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                升级会员
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}

      {/* 安全设置 */}
      {activeSection === 'security' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>修改密码</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="当前密码"
                type="password"
                placeholder="请输入当前密码"
              />
              <Input
                label="新密码"
                type="password"
                placeholder="请输入新密码"
              />
              <Input
                label="确认新密码"
                type="password"
                placeholder="请再次输入新密码"
              />
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                更新密码
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>安全日志</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">登录成功</p>
                    <p className="text-xs text-gray-500">来自 127.0.0.1</p>
                  </div>
                  <p className="text-xs text-gray-500">刚刚</p>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">密码修改</p>
                    <p className="text-xs text-gray-500">安全操作</p>
                  </div>
                  <p className="text-xs text-gray-500">2天前</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 偏好设置 */}
      {activeSection === 'preferences' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>上传偏好</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">默认上传接口</label>
                <select className="input">
                  <option>自动选择</option>
                  <option>Local Storage</option>
                  <option>Imgur</option>
                  <option>Cloudinary</option>
                </select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">图片压缩</label>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="compress" className="rounded" />
                  <label htmlFor="compress" className="text-sm text-gray-600">
                    自动压缩大于1MB的图片
                  </label>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">水印设置</label>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="watermark" className="rounded" />
                  <label htmlFor="watermark" className="text-sm text-gray-600">
                    自动添加水印
                  </label>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                保存设置
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">邮件通知</label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input type="checkbox" id="upload-success" className="rounded" />
                    <label htmlFor="upload-success" className="text-sm text-gray-600">
                      上传成功通知
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input type="checkbox" id="upload-failed" className="rounded" />
                    <label htmlFor="upload-failed" className="text-sm text-gray-600">
                      上传失败通知
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input type="checkbox" id="level-expire" className="rounded" />
                    <label htmlFor="level-expire" className="text-sm text-gray-600">
                      会员到期提醒
                    </label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full">
                保存设置
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  );
}
