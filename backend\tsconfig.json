{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "typeRoots": ["./node_modules/@types", "./src/types"], "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}