import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { SocketService, LogData, LogFilters } from '../../services/socket.service';
import { useAuth } from '../../contexts/AuthContext';

interface LogEntry extends LogData {
  id: number;
  type: string;
  createdAt: string;
}

export function LogsManagement() {
  const { user } = useAuth();
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LogFilters>({});
  const [autoScroll, setAutoScroll] = useState(true);
  const [connectedClients, setConnectedClients] = useState(0);

  const logsContainerRef = useRef<HTMLDivElement>(null);
  const socketService = SocketService.getInstance();

  useEffect(() => {
    initializeSocket();
    return () => {
      socketService.disconnect();
    };
  }, []);

  useEffect(() => {
    if (autoScroll && logsContainerRef.current) {
      logsContainerRef.current.scrollTop = logsContainerRef.current.scrollHeight;
    }
  }, [logs, autoScroll]);

  const initializeSocket = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 获取用户 token
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      // 连接 Socket.IO
      await socketService.connect(token);
      setIsConnected(true);

      // 设置事件监听器
      const unsubscribeLog = socketService.onLog((log: LogData) => {
        const logEntry: LogEntry = {
          ...log,
          type: log.type || 'unknown'
        };

        setLogs(prevLogs => {
          const newLogs = [logEntry, ...prevLogs];
          // 限制日志数量，保持最新的 1000 条
          return newLogs.slice(0, 1000);
        });
      });

      const unsubscribeConnection = socketService.onConnectionStatus((status) => {
        setConnectedClients(status.clientsCount);
      });

      const unsubscribeError = socketService.onError((error) => {
        console.error('Socket 错误:', error);
        setError('实时连接出现错误');
      });

      // 订阅日志
      socketService.subscribeToLogs(filters);

      // 获取初始日志数据
      await loadInitialLogs();

    } catch (error) {
      console.error('初始化 Socket 失败:', error);
      setError(error instanceof Error ? error.message : '连接失败');
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  const loadInitialLogs = async () => {
    try {
      const response = await fetch('/api/admin/logs/realtime?limit=100', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setLogs(data.data || []);
        }
      }
    } catch (error) {
      console.error('加载初始日志失败:', error);
    }
  };

  const handleFilterChange = (newFilters: LogFilters) => {
    setFilters(newFilters);
    socketService.unsubscribeFromLogs();
    socketService.subscribeToLogs(newFilters);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const toggleAutoScroll = () => {
    setAutoScroll(!autoScroll);
  };

  const getLogLevelBadge = (level: string) => {
    const levelMap = {
      debug: { variant: 'secondary' as const, label: 'DEBUG' },
      info: { variant: 'default' as const, label: 'INFO' },
      warning: { variant: 'warning' as const, label: 'WARN' },
      error: { variant: 'error' as const, label: 'ERROR' },
      critical: { variant: 'error' as const, label: 'CRITICAL' },
    };
    return levelMap[level as keyof typeof levelMap] || { variant: 'secondary' as const, label: level.toUpperCase() };
  };

  const getLogTypeBadge = (type: string) => {
    const typeMap = {
      system: { variant: 'default' as const, label: '系统' },
      upload: { variant: 'success' as const, label: '上传' },
      access: { variant: 'secondary' as const, label: '访问' },
      admin: { variant: 'warning' as const, label: '管理' },
    };
    return typeMap[type as keyof typeof typeMap] || { variant: 'secondary' as const, label: type };
  };

  const formatLogMessage = (log: LogEntry) => {
    switch (log.type) {
      case 'system':
        return (log as any).eventMessage;
      case 'upload':
        const uploadLog = log as any;
        return `用户 ${uploadLog.username || uploadLog.userId} ${uploadLog.actionType} ${uploadLog.fileName || '文件'}`;
      case 'access':
        const accessLog = log as any;
        return `图片 ${accessLog.imageId} 被访问，来源 IP: ${accessLog.ipAddress}`;
      case 'admin':
        const adminLog = log as any;
        return `管理员 ${adminLog.adminUsername || adminLog.adminId} 执行 ${adminLog.operationType}`;
      default:
        return JSON.stringify(log);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
              <p className="text-gray-600">正在连接实时日志服务...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 连接状态和控制面板 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>实时系统日志</CardTitle>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                <span className="text-sm text-gray-600">
                  {isConnected ? '已连接' : '未连接'}
                </span>
                {isConnected && (
                  <span className="text-xs text-gray-500">
                    ({connectedClients} 个管理员在线)
                  </span>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <Button
                size="sm"
                variant="outline"
                onClick={clearLogs}
              >
                清空日志
              </Button>
              <Button
                size="sm"
                variant={autoScroll ? "default" : "outline"}
                onClick={toggleAutoScroll}
              >
                {autoScroll ? '停止滚动' : '自动滚动'}
              </Button>
            </div>
            <div className="text-sm text-gray-500">
              共 {logs.length} 条日志
            </div>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
              <Button
                size="sm"
                variant="outline"
                onClick={initializeSocket}
                className="mt-2"
              >
                重新连接
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 日志显示区域 */}
      <Card>
        <CardContent className="p-0">
          <div
            ref={logsContainerRef}
            className="h-96 overflow-y-auto bg-gray-50"
          >
            {logs.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">暂无日志数据</p>
              </div>
            ) : (
              <div className="space-y-1 p-4">
                {logs.map((log, index) => (
                  <div
                    key={`${log.id}-${index}`}
                    className="flex items-start space-x-3 p-3 bg-white rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    {/* 时间戳 */}
                    <div className="flex-shrink-0 w-20 text-xs text-gray-500 font-mono">
                      {new Date(log.createdAt).toLocaleTimeString()}
                    </div>

                    {/* 日志类型标签 */}
                    <div className="flex-shrink-0">
                      <Badge variant={getLogTypeBadge(log.type).variant}>
                        {getLogTypeBadge(log.type).label}
                      </Badge>
                    </div>

                    {/* 日志级别标签（仅系统日志） */}
                    {log.type === 'system' && (
                      <div className="flex-shrink-0">
                        <Badge variant={getLogLevelBadge((log as any).eventLevel).variant}>
                          {getLogLevelBadge((log as any).eventLevel).label}
                        </Badge>
                      </div>
                    )}

                    {/* 日志内容 */}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900 break-words">
                        {formatLogMessage(log)}
                      </p>

                      {/* 额外信息 */}
                      <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                        {log.type === 'upload' && (
                          <>
                            <span>IP: {(log as any).ipAddress}</span>
                            {(log as any).fileSize && (
                              <span>大小: {Math.round((log as any).fileSize / 1024)}KB</span>
                            )}
                          </>
                        )}
                        {log.type === 'system' && (log as any).serverInstance && (
                          <span>服务器: {(log as any).serverInstance}</span>
                        )}
                        {log.type === 'access' && (
                          <>
                            <span>IP: {(log as any).ipAddress}</span>
                            {(log as any).responseTime && (
                              <span>响应时间: {(log as any).responseTime}ms</span>
                            )}
                          </>
                        )}
                      </div>

                      {/* 详细数据（可展开） */}
                      {((log.type === 'system' && (log as any).eventData) ||
                        (log.type === 'admin' && (log as any).operationDetails)) && (
                        <details className="mt-2">
                          <summary className="text-xs text-blue-600 cursor-pointer hover:text-blue-800">
                            查看详细信息
                          </summary>
                          <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                            {JSON.stringify(
                              log.type === 'system'
                                ? (log as any).eventData
                                : (log as any).operationDetails,
                              null,
                              2
                            )}
                          </pre>
                        </details>
                      )}
                    </div>

                    {/* 状态指示器 */}
                    <div className="flex-shrink-0">
                      {log.type === 'upload' && (
                        <div className={`w-2 h-2 rounded-full ${
                          (log as any).isSuccess ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 日志统计 */}
      <Card>
        <CardHeader>
          <CardTitle>日志统计</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['system', 'upload', 'access', 'admin'].map(type => {
              const count = logs.filter(log => log.type === type).length;
              const typeBadge = getLogTypeBadge(type);

              return (
                <div key={type} className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {count}
                  </div>
                  <Badge variant={typeBadge.variant}>
                    {typeBadge.label}
                  </Badge>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
