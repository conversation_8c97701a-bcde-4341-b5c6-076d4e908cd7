import React, { createContext, useContext, useEffect, useState } from 'react';
import { auth } from '../lib/api';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  userLevel: string;
  status: string;
  avatarUrl?: string;
  levelExpiresAt?: string;
  createdAt: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string, confirmPassword: string, acceptTerms: boolean, acceptPrivacy: boolean) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        setLoading(false);
        return;
      }

      auth.setToken(token);
      const response = await auth.getCurrentUser();
      
      if (response.success && response.data) {
        setUser(response.data);
      } else {
        // Token无效，清除本地存储
        localStorage.removeItem('auth_token');
        auth.setToken(null);
      }
    } catch (error) {
      console.error('检查认证状态失败:', error);
      localStorage.removeItem('auth_token');
      auth.setToken(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await auth.login({ email, password });
      
      if (response.success && response.data) {
        const { user: userData, token } = response.data;
        setUser(userData);
        auth.setToken(token);
      } else {
        throw new Error(response.error?.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  };

  const register = async (username: string, email: string, password: string, confirmPassword: string, acceptTerms: boolean, acceptPrivacy: boolean) => {
    try {
      const response = await auth.register({
        username,
        email,
        password,
        confirmPassword,
        acceptTerms,
        acceptPrivacy
      });
      
      if (response.success && response.data) {
        const { user: userData, token } = response.data;
        setUser(userData);
        auth.setToken(token);
      } else {
        throw new Error(response.error?.message || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await auth.logout();
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      setUser(null);
      auth.setToken(null);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
