// API配置
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

// API端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
  },
  
  // 用户相关
  USER: {
    PROFILE: '/user/profile',
    UPDATE: '/user/update',
    SETTINGS: '/user/settings',
  },
  
  // 上传相关
  UPLOAD: {
    UPLOAD: '/upload',
    HISTORY: '/upload/history',
    DELETE: '/upload/delete',
  },
  
  // 管理端相关
  ADMIN: {
    STATS: '/admin/stats',
    USERS: '/admin/users',
    USER_LEVEL: '/admin/users/:id/level',
    USER_STATUS: '/admin/users/:id/status',
    USER_DETAILS: '/admin/users/:id',
    LOGS_REALTIME: '/admin/logs/realtime',
    LOGS_SYSTEM: '/admin/logs/system',
    PROVIDERS: '/admin/providers',
    MONITORING: '/admin/monitoring/system',
    SECURITY_IPS: '/admin/security/ips',
    ANALYTICS_OVERVIEW: '/admin/analytics/overview',
    ANALYTICS_USERS: '/admin/analytics/users',
    ANALYTICS_UPLOADS: '/admin/analytics/uploads',
  }
};

// HTTP方法
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
} as const;

// 请求配置
export const REQUEST_CONFIG = {
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
};
