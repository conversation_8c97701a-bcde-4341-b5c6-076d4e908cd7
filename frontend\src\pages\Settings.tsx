import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Badge } from '../components/ui/Badge';
import { useAuth } from '../contexts/AuthContext';
import { ProfileSettings } from '../components/settings/ProfileSettings';
import { PasswordSettings } from '../components/settings/PasswordSettings';
import { NotificationSettings } from '../components/settings/NotificationSettings';
import { UploadSettings } from '../components/settings/UploadSettings';
import { ActivityLogs } from '../components/settings/ActivityLogs';

type SettingsTab = 'profile' | 'password' | 'notifications' | 'upload' | 'activity' | 'security';

interface UserProfile {
  id: number;
  username: string;
  email: string;
  displayName?: string;
  bio?: string;
  location?: string;
  website?: string;
  avatarUrl?: string;
  role: string;
  userLevel: string;
  status: string;
  profileVisibility: string;
  allowDirectMessages: boolean;
  showOnlineStatus: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  marketingEmails: boolean;
  defaultImageQuality: number;
  autoCompress: boolean;
  defaultImageFormat: string;
  maxImageSize: number;
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

export function Settings() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<SettingsTab>('profile');
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const tabs = [
    { id: 'profile' as const, label: '个人信息', icon: '👤', description: '管理您的个人资料和隐私设置' },
    { id: 'password' as const, label: '密码安全', icon: '🔒', description: '修改密码和安全设置' },
    { id: 'notifications' as const, label: '通知设置', icon: '🔔', description: '管理通知偏好' },
    { id: 'upload' as const, label: '上传设置', icon: '📤', description: '配置图片上传偏好' },
    { id: 'activity' as const, label: '活动记录', icon: '📊', description: '查看账户活动历史' },
    { id: 'security' as const, label: '安全中心', icon: '🛡️', description: '两步验证和登录历史' },
  ];

  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = localStorage.getItem('auth_token');
      console.log('Token from localStorage:', token ? 'exists' : 'missing');

      if (!token) {
        throw new Error('未找到认证令牌，请重新登录');
      }

      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      const data = await response.json();
      console.log('Response data:', data);

      if (!response.ok) {
        if (data.error?.code === 'AUTH_002' || data.error?.code === 'TOKEN_EXPIRED') {
          // 令牌无效或过期，清除本地存储并重新登录
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          window.location.href = '/';
          return;
        }
        throw new Error(data.error?.message || '获取用户信息失败');
      }

      if (data.success) {
        setUserProfile(data.data);
      } else {
        throw new Error(data.error?.message || '获取用户信息失败');
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      setError(error instanceof Error ? error.message : '加载失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleProfileUpdate = (updatedData: Partial<UserProfile>) => {
    if (userProfile) {
      setUserProfile({ ...userProfile, ...updatedData });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
          <p className="text-gray-600">正在加载设置...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="text-center py-8">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={loadUserProfile}>重试</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-lg font-bold text-gray-900">账户设置</h1>
              <Badge variant="secondary">{userProfile?.userLevel}</Badge>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-xs text-gray-600">欢迎，{userProfile?.username}</span>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                {userProfile?.avatarUrl ? (
                  <img 
                    src={userProfile.avatarUrl} 
                    alt="头像" 
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-xs text-gray-600">
                    {userProfile?.username?.charAt(0).toUpperCase()}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 左侧导航 */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-0">
                <nav className="space-y-1">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full text-left px-4 py-3 text-xs hover:bg-gray-50 transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-50 border-r-2 border-blue-500 text-blue-700'
                          : 'text-gray-700'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-base">{tab.icon}</span>
                        <div>
                          <div className="font-medium">{tab.label}</div>
                          <div className="text-xs text-gray-500 mt-1">{tab.description}</div>
                        </div>
                      </div>
                    </button>
                  ))}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* 右侧内容 */}
          <div className="lg:col-span-3">
            {activeTab === 'profile' && userProfile && (
              <ProfileSettings 
                userProfile={userProfile} 
                onUpdate={handleProfileUpdate}
              />
            )}
            {activeTab === 'password' && (
              <PasswordSettings />
            )}
            {activeTab === 'notifications' && userProfile && (
              <NotificationSettings 
                userProfile={userProfile} 
                onUpdate={handleProfileUpdate}
              />
            )}
            {activeTab === 'upload' && userProfile && (
              <UploadSettings 
                userProfile={userProfile} 
                onUpdate={handleProfileUpdate}
              />
            )}
            {activeTab === 'activity' && (
              <ActivityLogs />
            )}
            {activeTab === 'security' && userProfile && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>安全中心</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 text-xs">安全功能正在开发中...</p>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
