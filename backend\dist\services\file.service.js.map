{"version": 3, "file": "file.service.js", "sourceRoot": "", "sources": ["../../src/services/file.service.ts"], "names": [], "mappings": ";;;;;;AAAA,2DAA6B;AAC7B,gDAAwB;AACxB,uCAAuC;AACvC,oDAA4B;AAE5B,MAAa,WAAW;IAEtB,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAc,EAAE,QAAgB;QACpD,IAAI,CAAC;YAEH,MAAM,WAAW,CAAC,eAAe,EAAE,CAAC;YAGpC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,YAAM,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAGxD,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAErC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,OAAO,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAgB;QACtC,IAAI,CAAC;YACH,MAAM,kBAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAElC,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAgB;QACtC,IAAI,CAAC;YACH,MAAM,kBAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,QAAgB;QAKvC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,OAAO;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;aACvB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAGO,MAAM,CAAC,KAAK,CAAC,eAAe;QAClC,IAAI,CAAC;YACH,MAAM,kBAAE,CAAC,MAAM,CAAC,YAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC;QAAC,MAAM,CAAC;YAEP,MAAM,kBAAE,CAAC,KAAK,CAAC,YAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,oBAAoB,CAAC,YAAoB,EAAE,IAAa;QAC7D,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAI,IAAI,gBAAM,CAAC,UAAU,EAAE,CAAC;QAC7C,OAAO,GAAG,QAAQ,GAAG,GAAG,EAAE,CAAC;IAC7B,CAAC;IAGD,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QACtC,MAAM,YAAY,GAAG,YAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,OAAO,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAGD,MAAM,CAAC,eAAe,CAAC,IAAY,EAAE,OAAgB;QACnD,MAAM,KAAK,GAAG,OAAO,IAAI,YAAM,CAAC,MAAM,CAAC,WAAW,CAAC;QACnD,OAAO,IAAI,IAAI,KAAK,CAAC;IACvB,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,YAAoB,QAAQ;QAC3E,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC3C,OAAO,gBAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,mBAAmB,CAAC,MAAc,EAAE,YAAoB,QAAQ;QACrE,OAAO,gBAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnE,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,GAAG,QAAQ,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YACtD,MAAM,MAAM,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,kBAAE,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACvC,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAyB,EAAE;QACvD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,YAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAGrD,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAE1E,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC1C,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEtC,IAAI,KAAK,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC;oBAC7B,MAAM,kBAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,WAAW,QAAQ,EAAE,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAe;QAC3C,IAAI,CAAC;YACH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAEjE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE/C,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBACvB,SAAS,IAAI,MAAM,WAAW,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAC5D,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACtC,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC;gBAC1B,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,cAAc,CAAC,KAAa;QACjC,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,IAAI,IAAI,GAAG,KAAK,CAAC;QACjB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,OAAO,IAAI,IAAI,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,IAAI,IAAI,IAAI,CAAC;YACb,SAAS,EAAE,CAAC;QACd,CAAC;QAED,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;IAClD,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAgB,EAAE,YAAoB;QACrE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACjE,OAAO,UAAU,KAAK,YAAY,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YACnC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAC3B,UAAkB,EAClB,gBAA0B;QAE1B,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE7C,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;gBACxC,IAAI,CAAC;oBAEH,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACvC,MAAM,kBAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;oBAG7C,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBACrC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,SAAS,QAAQ,MAAM,EAAE,KAAK,CAAC,CAAC;oBAC9C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC7B,CAAC;IAGD,MAAM,CAAC,wBAAwB,CAAC,QAAgB;QAC9C,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,SAAS,GAA8B;YAC3C,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,cAAc;SACvB,CAAC;QAEF,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,0BAA0B,CAAC;IACtD,CAAC;IAGD,MAAM,CAAC,qBAAqB,CAAC,YAAoB,EAAE,IAAY;QAC7D,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAClD,OAAO,GAAG,QAAQ,UAAU,IAAI,GAAG,GAAG,EAAE,CAAC;IAC3C,CAAC;CACF;AApQD,kCAoQC"}