import { SocketService } from '../config/socket';
import { SystemLogData, UploadLogData, LogHistoryParams } from '../config/socket';
export declare class LogService {
    private static socketService;
    static initialize(socketService: SocketService): void;
    static logSystemEvent(eventType: string, eventLevel: 'debug' | 'info' | 'warning' | 'error' | 'critical', eventMessage: string, eventData?: any, serverInstance?: string): Promise<void>;
    static logUploadAction(userId: number, imageId: number | null, actionType: 'upload' | 'reuse' | 'failed', fileName?: string, fileSize?: number, ipAddress?: string, userAgent?: string, isSuccess?: boolean, errorMessage?: string): Promise<void>;
    static logImageAccess(imageId: number, ipAddress: string, userAgent?: string, referer?: string, responseTime?: number): Promise<void>;
    static logAdminOperation(adminId: number, operationType: string, targetType?: string, targetId?: number, operationDetails?: any, ipAddress?: string, userAgent?: string): Promise<void>;
    static getSystemLogs(params: LogHistoryParams): Promise<{
        logs: SystemLogData[];
        total: number;
        page: number;
        limit: number;
    }>;
    static getUploadLogs(params: LogHistoryParams): Promise<{
        logs: UploadLogData[];
        total: number;
        page: number;
        limit: number;
    }>;
}
//# sourceMappingURL=log.service.d.ts.map