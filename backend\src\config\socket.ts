import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import jwt from 'jsonwebtoken';
import { config } from './env';
import { prisma } from './database';

// Socket.IO 事件类型定义
export interface ServerToClientEvents {
  // 实时日志事件
  'log:system': (log: SystemLogData) => void;
  'log:upload': (log: UploadLogData) => void;
  'log:access': (log: AccessLogData) => void;
  'log:admin': (log: AdminLogData) => void;
  
  // 系统监控事件
  'monitoring:system': (data: SystemMonitoringData) => void;
  'monitoring:alert': (alert: SystemAlert) => void;
  
  // 连接状态事件
  'connection:status': (status: ConnectionStatus) => void;
  'error': (error: SocketError) => void;
}

export interface ClientToServerEvents {
  // 客户端请求事件
  'logs:subscribe': (filters: LogFilters) => void;
  'logs:unsubscribe': () => void;
  'logs:history': (params: LogHistoryParams, callback: (data: any) => void) => void;
  
  // 监控订阅
  'monitoring:subscribe': () => void;
  'monitoring:unsubscribe': () => void;
}

export interface InterServerEvents {
  // 服务器间通信（集群模式）
  ping: () => void;
}

export interface SocketData {
  userId: number;
  username: string;
  role: string;
  isAdmin: boolean;
}

// 日志数据类型
export interface SystemLogData {
  id: number;
  eventType: string;
  eventLevel: string;
  eventMessage: string;
  eventData?: any;
  serverInstance?: string | undefined;
  createdAt: string;
}

export interface UploadLogData {
  id: number;
  userId: number;
  username?: string | undefined;
  imageId?: number | undefined;
  actionType: string;
  fileName?: string | undefined;
  fileSize?: number | undefined;
  ipAddress: string;
  userAgent?: string | undefined;
  isSuccess: boolean;
  errorMessage?: string | undefined;
  createdAt: string;
}

export interface AccessLogData {
  id: number;
  imageId: number;
  ipAddress: string;
  userAgent?: string | undefined;
  referer?: string | undefined;
  country?: string | undefined;
  city?: string | undefined;
  responseTime?: number | undefined;
  createdAt: string;
}

export interface AdminLogData {
  id: number;
  adminId: number;
  adminUsername?: string | undefined;
  operationType: string;
  targetType?: string | undefined;
  targetId?: number | undefined;
  operationDetails?: any;
  ipAddress?: string | undefined;
  userAgent?: string | undefined;
  createdAt: string;
}

export interface SystemMonitoringData {
  cpu: number;
  memory: number;
  disk: number;
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  activeConnections: number;
  timestamp: string;
}

export interface SystemAlert {
  type: 'high_cpu' | 'high_memory' | 'high_disk' | 'high_connections' | 'error_rate';
  level: 'warning' | 'critical';
  message: string;
  value: number;
  threshold: number;
  timestamp: string;
}

export interface ConnectionStatus {
  connected: boolean;
  timestamp: string;
  clientsCount: number;
}

export interface SocketError {
  code: string;
  message: string;
  timestamp: string;
}

export interface LogFilters {
  types?: string[];
  levels?: string[];
  startDate?: string;
  endDate?: string;
  userId?: number;
  ipAddress?: string;
}

export interface LogHistoryParams {
  page: number;
  limit: number;
  filters?: LogFilters;
}

// Socket.IO 服务器类
export class SocketService {
  private static instance: SocketService;
  private io: SocketIOServer<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;
  private connectedAdmins: Set<string> = new Set();

  private constructor(httpServer: HttpServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
      },
      path: '/socket.io',
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000,
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  // 获取单例实例
  public static getInstance(httpServer?: HttpServer): SocketService {
    if (!SocketService.instance && httpServer) {
      SocketService.instance = new SocketService(httpServer);
    }
    return SocketService.instance;
  }

  // 设置中间件
  private setupMiddleware(): void {
    // JWT 认证中间件
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('认证令牌缺失'));
        }

        const decoded = jwt.verify(token, config.jwtSecret) as any;
        
        // 验证用户是否存在且为管理员
        const user = await prisma.user.findUnique({
          where: { id: decoded.userId },
          select: {
            id: true,
            username: true,
            role: true,
            status: true,
          }
        });

        if (!user) {
          return next(new Error('用户不存在'));
        }

        if (user.status !== 'active') {
          return next(new Error('用户账户已被禁用'));
        }

        if (user.role !== 'admin') {
          return next(new Error('权限不足，仅管理员可访问'));
        }

        // 设置 socket 数据
        socket.data = {
          userId: user.id,
          username: user.username,
          role: user.role,
          isAdmin: true,
        };

        next();
      } catch (error) {
        console.error('Socket 认证失败:', error);
        next(new Error('认证失败'));
      }
    });
  }

  // 设置事件处理器
  private setupEventHandlers(): void {
    this.io.on('connection', (socket) => {
      console.log(`管理员 ${socket.data.username} 连接到实时日志系统`);
      
      // 加入管理员房间
      socket.join('admin-room');
      this.connectedAdmins.add(socket.id);

      // 发送连接状态
      socket.emit('connection:status', {
        connected: true,
        timestamp: new Date().toISOString(),
        clientsCount: this.connectedAdmins.size,
      });

      // 处理日志订阅
      socket.on('logs:subscribe', (filters) => {
        console.log(`管理员 ${socket.data.username} 订阅日志，过滤条件:`, filters);
        socket.join('logs-subscribers');
        // 这里可以根据过滤条件设置特定的房间
      });

      socket.on('logs:unsubscribe', () => {
        console.log(`管理员 ${socket.data.username} 取消订阅日志`);
        socket.leave('logs-subscribers');
      });

      // 处理历史日志请求
      socket.on('logs:history', async (params, callback) => {
        try {
          // 这里将在后续实现具体的历史日志查询逻辑
          callback({ success: true, data: [], total: 0 });
        } catch (error) {
          callback({ success: false, error: '获取历史日志失败' });
        }
      });

      // 处理监控订阅
      socket.on('monitoring:subscribe', () => {
        console.log(`管理员 ${socket.data.username} 订阅系统监控`);
        socket.join('monitoring-subscribers');
      });

      socket.on('monitoring:unsubscribe', () => {
        console.log(`管理员 ${socket.data.username} 取消订阅系统监控`);
        socket.leave('monitoring-subscribers');
      });

      // 处理断开连接
      socket.on('disconnect', (reason) => {
        console.log(`管理员 ${socket.data.username} 断开连接，原因: ${reason}`);
        this.connectedAdmins.delete(socket.id);
        
        // 通知其他管理员连接数变化
        this.io.to('admin-room').emit('connection:status', {
          connected: false,
          timestamp: new Date().toISOString(),
          clientsCount: this.connectedAdmins.size,
        });
      });
    });
  }

  // 广播系统日志
  public broadcastSystemLog(log: SystemLogData): void {
    this.io.to('logs-subscribers').emit('log:system', log);
  }

  // 广播上传日志
  public broadcastUploadLog(log: UploadLogData): void {
    this.io.to('logs-subscribers').emit('log:upload', log);
  }

  // 广播访问日志
  public broadcastAccessLog(log: AccessLogData): void {
    this.io.to('logs-subscribers').emit('log:access', log);
  }

  // 广播管理员操作日志
  public broadcastAdminLog(log: AdminLogData): void {
    this.io.to('logs-subscribers').emit('log:admin', log);
  }

  // 广播系统监控数据
  public broadcastSystemMonitoring(data: SystemMonitoringData): void {
    this.io.to('monitoring-subscribers').emit('monitoring:system', data);
  }

  // 广播系统告警
  public broadcastSystemAlert(alert: SystemAlert): void {
    this.io.to('admin-room').emit('monitoring:alert', alert);
  }

  // 获取连接的管理员数量
  public getConnectedAdminsCount(): number {
    return this.connectedAdmins.size;
  }

  // 关闭 Socket.IO 服务器
  public close(): void {
    this.io.close();
  }
}
