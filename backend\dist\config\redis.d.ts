import Redis from 'ioredis';
export declare const redis: Redis;
export declare function connectRedis(): Promise<void>;
export declare function disconnectRedis(): Promise<void>;
export declare class RedisService {
    static set(key: string, value: any, ttl?: number): Promise<void>;
    static get<T>(key: string): Promise<T | null>;
    static del(key: string): Promise<void>;
    static exists(key: string): Promise<boolean>;
    static expire(key: string, ttl: number): Promise<void>;
    static incr(key: string): Promise<number>;
    static decr(key: string): Promise<number>;
}
//# sourceMappingURL=redis.d.ts.map