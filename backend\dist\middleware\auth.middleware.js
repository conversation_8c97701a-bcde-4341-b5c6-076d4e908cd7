"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.recordIP = exports.requestId = exports.optionalAuth = exports.requireLevel = exports.requireAdmin = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const env_1 = require("../config/env");
const database_1 = require("../config/database");
const types_1 = require("../types");
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            res.status(401).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.UNAUTHORIZED,
                    message: '访问令牌缺失',
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        const decoded = jsonwebtoken_1.default.verify(token, env_1.config.jwt.secret);
        const user = await database_1.prisma.user.findUnique({
            where: { id: decoded.userId },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
                userLevel: true,
                status: true,
                avatarUrl: true,
                levelExpiresAt: true,
                createdAt: true,
            }
        });
        if (!user) {
            res.status(401).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.UNAUTHORIZED,
                    message: '用户不存在',
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        if (user.status === 'banned') {
            res.status(403).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.FORBIDDEN,
                    message: '账户已被封禁',
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        if (user.status === 'suspended') {
            res.status(403).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.FORBIDDEN,
                    message: '账户已被暂停',
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        req.user = {
            id: user.id.toString(),
            username: user.username,
            email: user.email,
            role: user.role,
            userLevel: user.userLevel,
            status: user.status,
            avatarUrl: user.avatarUrl || undefined,
            levelExpiresAt: user.levelExpiresAt?.toISOString(),
            createdAt: user.createdAt.toISOString(),
        };
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            res.status(401).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.TOKEN_EXPIRED,
                    message: '访问令牌已过期',
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            res.status(401).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INVALID_TOKEN,
                    message: '无效的访问令牌',
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        console.error('认证中间件错误:', error);
        res.status(500).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.INTERNAL_ERROR,
                message: '服务器内部错误',
                timestamp: new Date().toISOString(),
            }
        });
    }
};
exports.authenticateToken = authenticateToken;
const requireAdmin = (req, res, next) => {
    if (!req.user) {
        res.status(401).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.UNAUTHORIZED,
                message: '未授权访问',
                timestamp: new Date().toISOString(),
            }
        });
        return;
    }
    if (req.user.role !== 'admin') {
        res.status(403).json({
            success: false,
            error: {
                code: types_1.ErrorCodes.FORBIDDEN,
                message: '需要管理员权限',
                timestamp: new Date().toISOString(),
            }
        });
        return;
    }
    next();
};
exports.requireAdmin = requireAdmin;
const requireLevel = (minLevel) => {
    return (req, res, next) => {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.UNAUTHORIZED,
                    message: '未授权访问',
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        const levelHierarchy = ['free', 'vip1', 'vip2', 'vip3'];
        const userLevelIndex = levelHierarchy.indexOf(req.user.userLevel);
        const requiredLevelIndex = levelHierarchy.indexOf(minLevel);
        if (userLevelIndex < requiredLevelIndex) {
            res.status(403).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INSUFFICIENT_LEVEL,
                    message: `需要 ${minLevel} 或更高等级`,
                    timestamp: new Date().toISOString(),
                }
            });
            return;
        }
        next();
    };
};
exports.requireLevel = requireLevel;
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            next();
            return;
        }
        const decoded = jsonwebtoken_1.default.verify(token, env_1.config.jwt.secret);
        const user = await database_1.prisma.user.findUnique({
            where: { id: decoded.userId },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
                userLevel: true,
                status: true,
                avatarUrl: true,
                levelExpiresAt: true,
                createdAt: true,
            }
        });
        if (user && user.status === 'active') {
            req.user = {
                id: user.id.toString(),
                username: user.username,
                email: user.email,
                role: user.role,
                userLevel: user.userLevel,
                status: user.status,
                avatarUrl: user.avatarUrl || undefined,
                levelExpiresAt: user.levelExpiresAt?.toISOString(),
                createdAt: user.createdAt.toISOString(),
            };
        }
        next();
    }
    catch (error) {
        next();
    }
};
exports.optionalAuth = optionalAuth;
const requestId = (req, res, next) => {
    req.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    res.setHeader('X-Request-ID', req.requestId);
    next();
};
exports.requestId = requestId;
const recordIP = async (req, res, next) => {
    try {
        const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
        const userAgent = req.get('User-Agent') || '';
        if (req.user) {
            console.log(`用户 ${req.user.id} 从 IP ${clientIP} 访问`);
        }
        next();
    }
    catch (error) {
        console.error('IP记录中间件错误:', error);
        next();
    }
};
exports.recordIP = recordIP;
//# sourceMappingURL=auth.middleware.js.map