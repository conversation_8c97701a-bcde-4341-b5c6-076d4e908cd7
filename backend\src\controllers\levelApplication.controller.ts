import { Request, Response } from 'express';
import { prisma } from '../config/database';
import { ApiResponse, ErrorCodes } from '../types';
import { LogService } from '../services/log.service';

export class LevelApplicationController {
  // 获取用户等级配置信息
  static async getLevelConfigs(req: Request, res: Response): Promise<void> {
    try {
      const levelConfigs = await prisma.userLevelConfig.findMany({
        orderBy: { level: 'asc' },
        select: {
          level: true,
          displayName: true,
          maxDailyUploads: true,
          maxFileSize: true,
          maxStorageSpace: true,
          canChooseProvider: true,
          prioritySupport: true,
          customDomain: true,
          visibleProviderCount: true,
        }
      });

      // 转换 BigInt 为 Number
      const serializedConfigs = levelConfigs.map(config => ({
        ...config,
        maxFileSize: Number(config.maxFileSize),
        maxStorageSpace: Number(config.maxStorageSpace)
      }));

      res.json({
        success: true,
        data: serializedConfigs,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取等级配置失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取等级配置失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 提交等级申请
  static async submitApplication(req: Request, res: Response): Promise<void> {
    try {
      const userId = parseInt((req as any).user.id);
      const {
        requestedLevel,
        reason,
        contactInfo,
        businessInfo,
        expectedUsage,
        additionalInfo
      } = req.body;

      // 验证输入
      if (!requestedLevel || !reason) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '申请等级和申请理由是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 获取用户当前信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, username: true, userLevel: true, email: true }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.USER_NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 检查是否已有待处理的申请
      const existingApplication = await prisma.userLevelApplication.findFirst({
        where: {
          userId,
          status: 'pending'
        }
      });

      if (existingApplication) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '您已有待处理的等级申请，请等待审核结果',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 验证申请的等级是否有效
      const validLevels = ['free', 'vip1', 'vip2', 'vip3'];
      if (!validLevels.includes(requestedLevel)) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '无效的等级申请',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 检查是否是降级申请（通常不允许）
      const levelOrder = { free: 0, vip1: 1, vip2: 2, vip3: 3 };
      if (levelOrder[requestedLevel as keyof typeof levelOrder] <= levelOrder[user.userLevel as keyof typeof levelOrder]) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '只能申请更高等级的会员',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 创建申请记录
      const application = await prisma.userLevelApplication.create({
        data: {
          userId,
          currentLevel: user.userLevel,
          requestedLevel,
          reason,
          contactInfo: contactInfo || null,
          businessInfo: businessInfo || null,
          expectedUsage: expectedUsage || null,
          additionalInfo: additionalInfo || null,
        },
        select: {
          id: true,
          currentLevel: true,
          requestedLevel: true,
          reason: true,
          status: true,
          createdAt: true
        }
      });

      // 记录活动日志
      await LogService.logSystemEvent(
        'level_application_submitted',
        'info',
        `用户 ${user.username} 提交了等级申请：${user.userLevel} -> ${requestedLevel}`,
        { 
          userId, 
          applicationId: application.id,
          currentLevel: user.userLevel,
          requestedLevel,
          reason: reason.substring(0, 100) // 只记录前100个字符
        }
      );

      res.status(201).json({
        success: true,
        data: application,
        message: '等级申请提交成功，请等待管理员审核',
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('提交等级申请失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '提交等级申请失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 获取用户的申请历史
  static async getUserApplications(req: Request, res: Response): Promise<void> {
    try {
      const userId = parseInt((req as any).user.id);
      const { page = 1, limit = 10 } = req.query;

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      const take = parseInt(limit as string);

      const [applications, total] = await Promise.all([
        prisma.userLevelApplication.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          skip,
          take,
          select: {
            id: true,
            currentLevel: true,
            requestedLevel: true,
            reason: true,
            status: true,
            adminComment: true,
            processedAt: true,
            createdAt: true,
            admin: {
              select: {
                username: true,
                displayName: true
              }
            }
          }
        }),
        prisma.userLevelApplication.count({ where: { userId } })
      ]);

      res.json({
        success: true,
        data: {
          applications,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取申请历史失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取申请历史失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 取消申请（仅限待处理状态）
  static async cancelApplication(req: Request, res: Response): Promise<void> {
    try {
      const userId = parseInt((req as any).user.id);
      const { applicationId } = req.params;

      if (!applicationId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '申请ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 查找申请记录
      const application = await prisma.userLevelApplication.findFirst({
        where: {
          id: parseInt(applicationId),
          userId,
          status: 'pending'
        }
      });

      if (!application) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '未找到可取消的申请记录',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 更新申请状态
      const updatedApplication = await prisma.userLevelApplication.update({
        where: { id: parseInt(applicationId) },
        data: {
          status: 'cancelled',
          updatedAt: new Date()
        },
        select: {
          id: true,
          status: true,
          updatedAt: true
        }
      });

      // 记录活动日志
      await LogService.logSystemEvent(
        'level_application_cancelled',
        'info',
        `用户取消了等级申请`,
        { userId, applicationId: parseInt(applicationId) }
      );

      res.json({
        success: true,
        data: updatedApplication,
        message: '申请已取消',
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('取消申请失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '取消申请失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }
}
