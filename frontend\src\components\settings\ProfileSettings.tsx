import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface UserProfile {
  id: number;
  username: string;
  email: string;
  displayName?: string;
  bio?: string;
  location?: string;
  website?: string;
  avatarUrl?: string;
  role: string;
  userLevel: string;
  status: string;
  profileVisibility: string;
  allowDirectMessages: boolean;
  showOnlineStatus: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

interface ProfileSettingsProps {
  userProfile: UserProfile;
  onUpdate: (updatedData: Partial<UserProfile>) => void;
}

export function ProfileSettings({ userProfile, onUpdate }: ProfileSettingsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    displayName: userProfile.displayName || '',
    bio: userProfile.bio || '',
    location: userProfile.location || '',
    website: userProfile.website || '',
    profileVisibility: userProfile.profileVisibility,
    allowDirectMessages: userProfile.allowDirectMessages,
    showOnlineStatus: userProfile.showOnlineStatus,
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('未找到认证令牌');
      }

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('更新失败');
      }

      const data = await response.json();
      if (data.success) {
        onUpdate(data.data);
        setIsEditing(false);
      } else {
        throw new Error(data.error?.message || '更新失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      alert(error instanceof Error ? error.message : '保存失败');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      displayName: userProfile.displayName || '',
      bio: userProfile.bio || '',
      location: userProfile.location || '',
      website: userProfile.website || '',
      profileVisibility: userProfile.profileVisibility,
      allowDirectMessages: userProfile.allowDirectMessages,
      showOnlineStatus: userProfile.showOnlineStatus,
    });
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>基本信息</CardTitle>
            {!isEditing ? (
              <Button size="sm" onClick={() => setIsEditing(true)}>
                编辑
              </Button>
            ) : (
              <div className="flex space-x-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  取消
                </Button>
                <Button 
                  size="sm" 
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? '保存中...' : '保存'}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 头像 */}
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
              {userProfile.avatarUrl ? (
                <img 
                  src={userProfile.avatarUrl} 
                  alt="头像" 
                  className="w-16 h-16 rounded-full object-cover"
                />
              ) : (
                <span className="text-xl text-gray-600">
                  {userProfile.username.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            <div>
              <h3 className="text-base font-medium text-gray-900">{userProfile.username}</h3>
              <p className="text-xs text-gray-500">{userProfile.email}</p>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="secondary">{userProfile.userLevel}</Badge>
                <Badge variant={userProfile.status === 'active' ? 'success' : 'error'}>
                  {userProfile.status}
                </Badge>
              </div>
            </div>
          </div>

          {/* 显示名称 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              显示名称
            </label>
            {isEditing ? (
              <input
                type="text"
                value={formData.displayName}
                onChange={(e) => handleInputChange('displayName', e.target.value)}
                className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入您的显示名称"
                maxLength={100}
              />
            ) : (
              <p className="text-xs text-gray-900">
                {userProfile.displayName || '未设置'}
              </p>
            )}
          </div>

          {/* 个人简介 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              个人简介
            </label>
            {isEditing ? (
              <textarea
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="介绍一下您自己..."
                rows={3}
                maxLength={500}
              />
            ) : (
              <p className="text-xs text-gray-900">
                {userProfile.bio || '未设置'}
              </p>
            )}
          </div>

          {/* 位置 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              位置
            </label>
            {isEditing ? (
              <input
                type="text"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="您的位置"
                maxLength={100}
              />
            ) : (
              <p className="text-xs text-gray-900">
                {userProfile.location || '未设置'}
              </p>
            )}
          </div>

          {/* 网站 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              个人网站
            </label>
            {isEditing ? (
              <input
                type="url"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://example.com"
                maxLength={200}
              />
            ) : (
              <p className="text-xs text-gray-900">
                {userProfile.website ? (
                  <a 
                    href={userProfile.website} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {userProfile.website}
                  </a>
                ) : (
                  '未设置'
                )}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 隐私设置 */}
      <Card>
        <CardHeader>
          <CardTitle>隐私设置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 资料可见性 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">
              资料可见性
            </label>
            {isEditing ? (
              <select
                value={formData.profileVisibility}
                onChange={(e) => handleInputChange('profileVisibility', e.target.value)}
                className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="public">公开</option>
                <option value="private">私密</option>
                <option value="friends">仅好友</option>
              </select>
            ) : (
              <p className="text-xs text-gray-900">
                {formData.profileVisibility === 'public' && '公开'}
                {formData.profileVisibility === 'private' && '私密'}
                {formData.profileVisibility === 'friends' && '仅好友'}
              </p>
            )}
          </div>

          {/* 允许私信 */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-xs font-medium text-gray-700">允许私信</label>
              <p className="text-xs text-gray-500">其他用户可以向您发送私信</p>
            </div>
            {isEditing ? (
              <input
                type="checkbox"
                checked={formData.allowDirectMessages}
                onChange={(e) => handleInputChange('allowDirectMessages', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            ) : (
              <Badge variant={userProfile.allowDirectMessages ? 'success' : 'secondary'}>
                {userProfile.allowDirectMessages ? '开启' : '关闭'}
              </Badge>
            )}
          </div>

          {/* 显示在线状态 */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-xs font-medium text-gray-700">显示在线状态</label>
              <p className="text-xs text-gray-500">其他用户可以看到您的在线状态</p>
            </div>
            {isEditing ? (
              <input
                type="checkbox"
                checked={formData.showOnlineStatus}
                onChange={(e) => handleInputChange('showOnlineStatus', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            ) : (
              <Badge variant={userProfile.showOnlineStatus ? 'success' : 'secondary'}>
                {userProfile.showOnlineStatus ? '开启' : '关闭'}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 账户信息 */}
      <Card>
        <CardHeader>
          <CardTitle>账户信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="text-gray-500">注册时间</span>
              <p className="text-gray-900 mt-1">
                {new Date(userProfile.createdAt).toLocaleDateString()}
              </p>
            </div>
            <div>
              <span className="text-gray-500">最后登录</span>
              <p className="text-gray-900 mt-1">
                {userProfile.lastLoginAt 
                  ? new Date(userProfile.lastLoginAt).toLocaleDateString()
                  : '未知'
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
