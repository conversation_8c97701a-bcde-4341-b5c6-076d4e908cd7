import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('开始数据库种子数据初始化...');

  // 创建用户等级配置
  const levelConfigs = [
    {
      level: 'free',
      displayName: '免费用户',
      maxDailyUploads: 10,
      maxFileSize: BigInt(5 * 1024 * 1024), // 5MB
      maxStorageSpace: BigInt(100 * 1024 * 1024), // 100MB
      canChooseProvider: false,
      prioritySupport: false,
      customDomain: false,
      visibleProviderCount: 2,
    },
    {
      level: 'vip1',
      displayName: 'VIP1用户',
      maxDailyUploads: 50,
      maxFileSize: BigInt(20 * 1024 * 1024), // 20MB
      maxStorageSpace: BigInt(1024 * 1024 * 1024), // 1GB
      canChooseProvider: true,
      prioritySupport: false,
      customDomain: false,
      visibleProviderCount: 4,
    },
    {
      level: 'vip2',
      displayName: 'VIP2用户',
      maxDailyUploads: 200,
      maxFileSize: BigInt(50 * 1024 * 1024), // 50MB
      maxStorageSpace: BigInt(5 * 1024 * 1024 * 1024), // 5GB
      canChooseProvider: true,
      prioritySupport: true,
      customDomain: false,
      visibleProviderCount: 6,
    },
    {
      level: 'vip3',
      displayName: 'VIP3用户',
      maxDailyUploads: 1000,
      maxFileSize: BigInt(100 * 1024 * 1024), // 100MB
      maxStorageSpace: BigInt(20 * 1024 * 1024 * 1024), // 20GB
      canChooseProvider: true,
      prioritySupport: true,
      customDomain: true,
      visibleProviderCount: 10,
    },
  ];

  for (const config of levelConfigs) {
    await prisma.userLevelConfig.upsert({
      where: { level: config.level },
      update: config,
      create: config,
    });
    console.log(`✅ 创建/更新用户等级配置: ${config.displayName}`);
  }

  // 创建默认上传接口
  const providers = [
    {
      name: 'Local Storage',
      description: '本地存储，用于系统内部访问',
      endpoint: 'http://localhost:3000/api/storage',
      status: 'active',
      priority: 1,
      maxFileSize: BigInt(100 * 1024 * 1024), // 100MB
      supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      requiredLevel: 'free',
      isPremium: false,
      costPerUpload: 0,
    },
    {
      name: 'Imgur',
      description: 'Imgur图片托管服务',
      endpoint: 'https://api.imgur.com/3/image',
      status: 'inactive', // 默认不激活，需要配置API密钥
      priority: 2,
      maxFileSize: BigInt(10 * 1024 * 1024), // 10MB
      supportedFormats: ['image/jpeg', 'image/png', 'image/gif'],
      requiredLevel: 'free',
      isPremium: false,
      costPerUpload: 0,
    },
    {
      name: 'Cloudinary',
      description: 'Cloudinary云端图片服务',
      endpoint: 'https://api.cloudinary.com/v1_1/upload',
      status: 'inactive', // 默认不激活，需要配置API密钥
      priority: 3,
      maxFileSize: BigInt(10 * 1024 * 1024), // 10MB
      supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      requiredLevel: 'vip1',
      isPremium: true,
      costPerUpload: 0.001,
    },
  ];

  for (const provider of providers) {
    const existing = await prisma.uploadProvider.findFirst({
      where: { name: provider.name }
    });

    if (!existing) {
      await prisma.uploadProvider.create({
        data: provider,
      });
      console.log(`✅ 创建/更新上传接口: ${provider.name}`);
    } else {
      console.log(`⚠️ 上传接口已存在: ${provider.name}`);
    }
  }

  // 创建等级接口可见性配置
  const visibilityConfigs = [
    // 免费用户
    { level: 'free', providerId: 1, isVisible: true, displayOrder: 1 }, // Local Storage
    { level: 'free', providerId: 2, isVisible: true, displayOrder: 2 }, // Imgur
    
    // VIP1用户
    { level: 'vip1', providerId: 1, isVisible: true, displayOrder: 1 }, // Local Storage
    { level: 'vip1', providerId: 2, isVisible: true, displayOrder: 2 }, // Imgur
    { level: 'vip1', providerId: 3, isVisible: true, displayOrder: 3 }, // Cloudinary
    
    // VIP2用户
    { level: 'vip2', providerId: 1, isVisible: true, displayOrder: 1 }, // Local Storage
    { level: 'vip2', providerId: 2, isVisible: true, displayOrder: 2 }, // Imgur
    { level: 'vip2', providerId: 3, isVisible: true, displayOrder: 3 }, // Cloudinary
    
    // VIP3用户
    { level: 'vip3', providerId: 1, isVisible: true, displayOrder: 1 }, // Local Storage
    { level: 'vip3', providerId: 2, isVisible: true, displayOrder: 2 }, // Imgur
    { level: 'vip3', providerId: 3, isVisible: true, displayOrder: 3 }, // Cloudinary
  ];

  for (const config of visibilityConfigs) {
    await prisma.levelProviderVisibility.upsert({
      where: {
        level_providerId: {
          level: config.level,
          providerId: config.providerId,
        }
      },
      update: config,
      create: config,
    });
  }
  console.log('✅ 创建等级接口可见性配置');

  // 创建IP风控规则
  const riskRules = [
    {
      ruleName: '上传频率限制',
      ruleType: 'upload_limit',
      timeWindow: 60, // 1小时
      maxAttempts: 100,
      blockDuration: 60, // 1小时
      isActive: true,
    },
    {
      ruleName: '登录频率限制',
      ruleType: 'login_limit',
      timeWindow: 15, // 15分钟
      maxAttempts: 5,
      blockDuration: 30, // 30分钟
      isActive: true,
    },
    {
      ruleName: '可疑活动检测',
      ruleType: 'suspicious_activity',
      timeWindow: 5, // 5分钟
      maxAttempts: 20,
      blockDuration: 120, // 2小时
      isActive: true,
    },
  ];

  for (const rule of riskRules) {
    const existing = await prisma.ipRiskRule.findFirst({
      where: { ruleName: rule.ruleName }
    });

    if (!existing) {
      await prisma.ipRiskRule.create({
        data: rule,
      });
      console.log(`✅ 创建IP风控规则: ${rule.ruleName}`);
    } else {
      console.log(`⚠️ IP风控规则已存在: ${rule.ruleName}`);
    }
  }

  // 创建系统配置
  const systemConfigs = [
    {
      key: 'site_name',
      value: 'LoftChat 智能图片上传管理系统',
      description: '网站名称',
    },
    {
      key: 'site_description',
      value: '基于多接口备份的智能图片上传管理分发系统',
      description: '网站描述',
    },
    {
      key: 'max_upload_size',
      value: 104857600, // 100MB
      description: '最大上传文件大小（字节）',
    },
    {
      key: 'allowed_file_types',
      value: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      description: '允许的文件类型',
    },
    {
      key: 'enable_registration',
      value: true,
      description: '是否允许用户注册',
    },
    {
      key: 'enable_email_verification',
      value: false,
      description: '是否启用邮箱验证',
    },
    {
      key: 'default_user_level',
      value: 'free',
      description: '新用户默认等级',
    },
  ];

  for (const config of systemConfigs) {
    await prisma.systemConfig.upsert({
      where: { key: config.key },
      update: { value: config.value, description: config.description },
      create: config,
    });
    console.log(`✅ 创建系统配置: ${config.key}`);
  }

  // 创建默认管理员用户
  const bcrypt = await import('bcrypt');
  const adminPassword = await bcrypt.hash('Admin123', 12);
  
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: adminPassword,
      role: 'admin',
      userLevel: 'vip3',
      status: 'active',
    },
  });
  console.log('✅ 创建默认管理员用户: <EMAIL> (密码: Admin123)');

  console.log('🎉 数据库种子数据初始化完成！');
}

main()
  .catch((e) => {
    console.error('❌ 数据库种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
