{"version": 3, "file": "socket.js", "sourceRoot": "", "sources": ["../../src/config/socket.ts"], "names": [], "mappings": ";;;;;;AAAA,yCAAqD;AAErD,gEAA+B;AAC/B,qCAAkC;AAClC,yCAAoC;AA8IpC,MAAa,aAAa;IAKxB,YAAoB,UAAsB;QAFlC,oBAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;QAG/C,IAAI,CAAC,EAAE,GAAG,IAAI,kBAAc,CAAC,UAAU,EAAE;YACvC,IAAI,EAAE;gBACJ,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;gBAC3D,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;gBACxB,WAAW,EAAE,IAAI;aAClB;YACD,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;YACpC,WAAW,EAAE,KAAK;YAClB,YAAY,EAAE,KAAK;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAGM,MAAM,CAAC,WAAW,CAAC,UAAuB;QAC/C,IAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,UAAU,EAAE,CAAC;YAC1C,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,CAAC,UAAU,CAAC,CAAC;QACzD,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAChC,CAAC;IAGO,eAAe;QAErB,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;YACjC,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBAE5G,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACnC,CAAC;gBAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,eAAM,CAAC,SAAS,CAAQ,CAAC;gBAG3D,MAAM,IAAI,GAAG,MAAM,iBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;oBAC7B,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI;qBACb;iBACF,CAAC,CAAC;gBAEH,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBAClC,CAAC;gBAED,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;oBAC7B,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBACrC,CAAC;gBAED,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC1B,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;gBACzC,CAAC;gBAGD,MAAM,CAAC,IAAI,GAAG;oBACZ,MAAM,EAAE,IAAI,CAAC,EAAE;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,IAAI;iBACd,CAAC;gBAEF,IAAI,EAAE,CAAC;YACT,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAGO,kBAAkB;QACxB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;YAClC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,YAAY,CAAC,CAAC;YAGrD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAGpC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;aACxC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,OAAO,EAAE,EAAE;gBACtC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,aAAa,EAAE,OAAO,CAAC,CAAC;gBAC/D,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAElC,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBACjC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,CAAC;gBAClD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE;gBACnD,IAAI,CAAC;oBAEH,QAAQ,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;gBAClD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;gBACrC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,CAAC;gBAClD,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;gBACvC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,WAAW,CAAC,CAAC;gBACpD,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;gBACjC,OAAO,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,aAAa,MAAM,EAAE,CAAC,CAAC;gBAC9D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAGvC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBACjD,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;iBACxC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAGM,kBAAkB,CAAC,GAAkB;QAC1C,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;IAGM,kBAAkB,CAAC,GAAkB;QAC1C,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;IAGM,kBAAkB,CAAC,GAAkB;QAC1C,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;IAGM,iBAAiB,CAAC,GAAiB;QACxC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IACxD,CAAC;IAGM,yBAAyB,CAAC,IAA0B;QACzD,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAGM,oBAAoB,CAAC,KAAkB;QAC5C,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAGM,uBAAuB;QAC5B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;IACnC,CAAC;IAGM,KAAK;QACV,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;IAClB,CAAC;CACF;AAzLD,sCAyLC"}