import { API_BASE_URL } from '../config/api';

export interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  totalUploads: number;
  todayUploads: number;
  totalStorage: number;
  systemLoad: {
    cpu: number;
    memory: number;
    disk: number;
  };
  onlineUsers: number;
}

export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  userLevel: string;
  status: 'active' | 'suspended' | 'banned';
  totalUploads: number;
  storageUsed: number;
  createdAt: string;
  levelExpiresAt?: string;
}

export interface UsersResponse {
  users: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    timestamp: string;
  };
  timestamp: string;
}

class AdminService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}/admin${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
    });

    const data: ApiResponse<T> = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || `HTTP error! status: ${response.status}`);
    }

    if (!data.success) {
      throw new Error(data.error?.message || 'API request failed');
    }

    return data.data as T;
  }

  async getSystemStats(): Promise<SystemStats> {
    return this.request<SystemStats>('/stats');
  }

  async getUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    level?: string;
    status?: string;
  } = {}): Promise<UsersResponse> {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.level && params.level !== 'all') queryParams.append('level', params.level);
    if (params.status && params.status !== 'all') queryParams.append('status', params.status);

    const endpoint = `/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<UsersResponse>(endpoint);
  }

  async updateUserLevel(userId: number, level: string, expiresAt?: string): Promise<User> {
    return this.request<User>(`/users/${userId}/level`, {
      method: 'PUT',
      body: JSON.stringify({ level, expiresAt }),
    });
  }

  async updateUserStatus(userId: number, status: string): Promise<User> {
    return this.request<User>(`/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }

  async getUserDetails(userId: number): Promise<User> {
    return this.request<User>(`/users/${userId}`);
  }

  async getRealtimeLogs(): Promise<any[]> {
    return this.request<any[]>('/logs/realtime');
  }
}

export const adminService = new AdminService();