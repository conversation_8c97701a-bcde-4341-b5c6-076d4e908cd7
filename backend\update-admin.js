const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function updateAdmin() {
  try {
    console.log('🔄 更新管理员密码...');
    
    // 新密码：Admin123 (符合验证规则：大小写字母、数字)
    const newPassword = 'Admin123';
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // 更新或创建管理员用户
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        passwordHash: hashedPassword,
      },
      create: {
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        role: 'admin',
        userLevel: 'vip3',
        status: 'active',
      },
    });
    
    console.log('✅ 管理员账号已更新！');
    console.log('📧 邮箱: <EMAIL>');
    console.log('🔑 密码: Admin123');
    console.log('👤 角色: admin');
    console.log('⭐ 等级: vip3');
    
  } catch (error) {
    console.error('❌ 更新失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateAdmin();
