"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.queryValidation = exports.adminValidation = exports.userValidation = exports.uploadValidation = exports.authValidation = void 0;
const joi_1 = __importDefault(require("joi"));
const commonValidation = {
    email: joi_1.default.string()
        .email({ tlds: { allow: false } })
        .max(100)
        .required()
        .messages({
        'string.email': '请输入有效的邮箱地址',
        'string.max': '邮箱地址不能超过100个字符',
        'any.required': '邮箱地址不能为空',
    }),
    password: joi_1.default.string()
        .min(8)
        .max(128)
        .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&\-_+=<>,.;:'"(){}[\]|\\\/~`^#]*$/)
        .required()
        .messages({
        'string.min': '密码至少需要8个字符',
        'string.max': '密码不能超过128个字符',
        'string.pattern.base': '密码必须包含大小写字母和数字',
        'any.required': '密码不能为空',
    }),
    username: joi_1.default.string()
        .alphanum()
        .min(3)
        .max(30)
        .required()
        .messages({
        'string.alphanum': '用户名只能包含字母和数字',
        'string.min': '用户名至少需要3个字符',
        'string.max': '用户名不能超过30个字符',
        'any.required': '用户名不能为空',
    }),
    id: joi_1.default.number()
        .integer()
        .positive()
        .required()
        .messages({
        'number.base': 'ID必须是数字',
        'number.integer': 'ID必须是整数',
        'number.positive': 'ID必须是正数',
        'any.required': 'ID不能为空',
    }),
    uuid: joi_1.default.string()
        .guid({ version: 'uuidv4' })
        .required()
        .messages({
        'string.guid': '无效的UUID格式',
        'any.required': 'UUID不能为空',
    }),
};
exports.authValidation = {
    register: joi_1.default.object({
        username: commonValidation.username,
        email: commonValidation.email,
        password: commonValidation.password,
        confirmPassword: joi_1.default.string()
            .valid(joi_1.default.ref('password'))
            .required()
            .messages({
            'any.only': '确认密码必须与密码一致',
            'any.required': '确认密码不能为空',
        }),
        acceptTerms: joi_1.default.boolean()
            .valid(true)
            .required()
            .messages({
            'any.only': '必须同意服务条款',
            'any.required': '必须同意服务条款',
        }),
        acceptPrivacy: joi_1.default.boolean()
            .valid(true)
            .required()
            .messages({
            'any.only': '必须同意隐私政策',
            'any.required': '必须同意隐私政策',
        }),
    }),
    login: joi_1.default.object({
        email: commonValidation.email,
        password: joi_1.default.string()
            .required()
            .messages({
            'any.required': '密码不能为空',
        }),
        rememberMe: joi_1.default.boolean().default(false),
    }),
    changePassword: joi_1.default.object({
        currentPassword: joi_1.default.string()
            .required()
            .messages({
            'any.required': '当前密码不能为空',
        }),
        newPassword: commonValidation.password,
        confirmPassword: joi_1.default.string()
            .valid(joi_1.default.ref('newPassword'))
            .required()
            .messages({
            'any.only': '确认密码必须与新密码一致',
            'any.required': '确认密码不能为空',
        }),
    }),
    forgotPassword: joi_1.default.object({
        email: commonValidation.email,
    }),
    resetPassword: joi_1.default.object({
        token: joi_1.default.string()
            .required()
            .messages({
            'any.required': '重置令牌不能为空',
        }),
        newPassword: commonValidation.password,
        confirmPassword: joi_1.default.string()
            .valid(joi_1.default.ref('newPassword'))
            .required()
            .messages({
            'any.only': '确认密码必须与新密码一致',
            'any.required': '确认密码不能为空',
        }),
    }),
};
exports.uploadValidation = {
    singleUpload: joi_1.default.object({
        description: joi_1.default.string()
            .max(500)
            .optional()
            .messages({
            'string.max': '描述不能超过500个字符',
        }),
        tags: joi_1.default.array()
            .items(joi_1.default.string().max(50))
            .max(10)
            .optional()
            .messages({
            'array.max': '标签数量不能超过10个',
            'string.max': '单个标签不能超过50个字符',
        }),
        isPrivate: joi_1.default.boolean().default(false),
        expiresAt: joi_1.default.date()
            .greater('now')
            .optional()
            .messages({
            'date.greater': '过期时间必须是未来时间',
        }),
    }),
    batchUpload: joi_1.default.object({
        files: joi_1.default.array()
            .items(joi_1.default.object({
            filename: joi_1.default.string().required(),
            description: joi_1.default.string().max(500).optional(),
        }))
            .min(1)
            .max(20)
            .required()
            .messages({
            'array.min': '至少需要上传1个文件',
            'array.max': '一次最多上传20个文件',
            'any.required': '文件列表不能为空',
        }),
        isPrivate: joi_1.default.boolean().default(false),
    }),
};
exports.userValidation = {
    updateProfile: joi_1.default.object({
        username: joi_1.default.string()
            .alphanum()
            .min(3)
            .max(30)
            .optional()
            .messages({
            'string.alphanum': '用户名只能包含字母和数字',
            'string.min': '用户名至少需要3个字符',
            'string.max': '用户名不能超过30个字符',
        }),
        avatarUrl: joi_1.default.string()
            .uri()
            .max(500)
            .optional()
            .messages({
            'string.uri': '头像URL格式无效',
            'string.max': 'URL长度不能超过500个字符',
        }),
    }),
    updateUserLevel: joi_1.default.object({
        userId: commonValidation.id,
        newLevel: joi_1.default.string()
            .valid('free', 'vip1', 'vip2', 'vip3')
            .required()
            .messages({
            'any.only': '用户等级必须是: free, vip1, vip2, vip3 之一',
            'any.required': '新等级不能为空',
        }),
        expiresAt: joi_1.default.date()
            .greater('now')
            .optional()
            .messages({
            'date.greater': '过期时间必须是未来时间',
        }),
        reason: joi_1.default.string()
            .max(200)
            .optional()
            .messages({
            'string.max': '原因说明不能超过200个字符',
        }),
    }),
    banUser: joi_1.default.object({
        userId: commonValidation.id,
        reason: joi_1.default.string()
            .max(500)
            .required()
            .messages({
            'string.max': '封禁原因不能超过500个字符',
            'any.required': '封禁原因不能为空',
        }),
        duration: joi_1.default.number()
            .integer()
            .positive()
            .optional()
            .messages({
            'number.integer': '封禁时长必须是整数',
            'number.positive': '封禁时长必须是正数',
        }),
    }),
};
exports.adminValidation = {
    addProvider: joi_1.default.object({
        name: joi_1.default.string()
            .max(100)
            .required()
            .messages({
            'string.max': '接口名称不能超过100个字符',
            'any.required': '接口名称不能为空',
        }),
        description: joi_1.default.string()
            .max(500)
            .optional()
            .messages({
            'string.max': '接口描述不能超过500个字符',
        }),
        endpoint: joi_1.default.string()
            .uri()
            .max(500)
            .required()
            .messages({
            'string.uri': '接口地址格式无效',
            'string.max': '接口地址不能超过500个字符',
            'any.required': '接口地址不能为空',
        }),
        apiKey: joi_1.default.string()
            .max(255)
            .optional()
            .messages({
            'string.max': 'API密钥不能超过255个字符',
        }),
        config: joi_1.default.object().optional(),
        priority: joi_1.default.number()
            .integer()
            .min(1)
            .max(100)
            .default(1)
            .messages({
            'number.integer': '优先级必须是整数',
            'number.min': '优先级最小值为1',
            'number.max': '优先级最大值为100',
        }),
        maxFileSize: joi_1.default.number()
            .integer()
            .positive()
            .default(10485760)
            .messages({
            'number.integer': '最大文件大小必须是整数',
            'number.positive': '最大文件大小必须是正数',
        }),
        supportedFormats: joi_1.default.array()
            .items(joi_1.default.string())
            .min(1)
            .required()
            .messages({
            'array.min': '至少需要支持一种文件格式',
            'any.required': '支持的文件格式不能为空',
        }),
        requiredLevel: joi_1.default.string()
            .valid('free', 'vip1', 'vip2', 'vip3')
            .default('free')
            .messages({
            'any.only': '所需等级必须是: free, vip1, vip2, vip3 之一',
        }),
        isPremium: joi_1.default.boolean().default(false),
        costPerUpload: joi_1.default.number()
            .precision(4)
            .min(0)
            .default(0)
            .messages({
            'number.precision': '上传成本最多4位小数',
            'number.min': '上传成本不能为负数',
        }),
    }),
    updateConfig: joi_1.default.object({
        key: joi_1.default.string()
            .max(100)
            .required()
            .messages({
            'string.max': '配置键不能超过100个字符',
            'any.required': '配置键不能为空',
        }),
        value: joi_1.default.any().required().messages({
            'any.required': '配置值不能为空',
        }),
        description: joi_1.default.string()
            .max(500)
            .optional()
            .messages({
            'string.max': '配置描述不能超过500个字符',
        }),
    }),
};
exports.queryValidation = {
    pagination: joi_1.default.object({
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(20),
        sortBy: joi_1.default.string().optional(),
        sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
    }),
    imageList: joi_1.default.object({
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(20),
        sortBy: joi_1.default.string().valid('createdAt', 'fileSize', 'accessCount').default('createdAt'),
        sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
        status: joi_1.default.string().valid('processing', 'completed', 'failed').optional(),
        mimeType: joi_1.default.string().optional(),
        dateFrom: joi_1.default.date().optional(),
        dateTo: joi_1.default.date().optional(),
        search: joi_1.default.string().max(100).optional(),
    }),
    userList: joi_1.default.object({
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(20),
        sortBy: joi_1.default.string().valid('createdAt', 'username', 'userLevel').default('createdAt'),
        sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
        role: joi_1.default.string().valid('user', 'admin').optional(),
        userLevel: joi_1.default.string().valid('free', 'vip1', 'vip2', 'vip3').optional(),
        status: joi_1.default.string().valid('active', 'suspended', 'banned').optional(),
        search: joi_1.default.string().max(100).optional(),
    }),
};
//# sourceMappingURL=validation.schemas.js.map